
import numpy as np
import torch
import hydra
import dill
from diffusion_policy.workspace.robotworkspace import RobotWorkspace
from diffusion_policy.env_runner.dp_runner import DPRunner
import os

class DP:
    def __init__(self, ckpt_folder:str):
        ckpt_file = os.path.join(ckpt_folder, '300.ckpt')
        self.policy = self.get_policy(ckpt_file, None, 'cuda:0')
        self.runner = DPRunner(output_dir=None)

    def update_obs(self, observation):
        self.runner.update_obs(observation)
    
    def get_action(self, observation=None):
        action = self.runner.get_action(self.policy, observation)
        return action

    def get_last_obs(self):
        return self.runner.obs[-1]

    def get_policy(self, checkpoint, output_dir, device):
        # load checkpoint
        payload = torch.load(open(checkpoint, 'rb'), pickle_module=dill)
        cfg = payload['cfg']
        cls = hydra.utils.get_class(cfg._target_)
        workspace = cls(cfg, output_dir=output_dir)
        workspace: RobotWorkspace
        workspace.load_payload(payload, exclude_keys=None, include_keys=None)
        
        # get policy from workspace
        policy = workspace.model
        if cfg.training.use_ema:
            policy = workspace.ema_model
        
        device = torch.device(device)
        policy.to(device)
        policy.eval()

        return policy

def encode_obs(observation): # For DP
    head_cam = np.moveaxis(observation['observation']['head_camera']['rgb'], -1, 0) / 255
    front_cam = np.moveaxis(observation['observation']['front_camera']['rgb'], -1, 0) / 255
    left_cam = np.moveaxis(observation['observation']['left_camera']['rgb'], -1, 0) / 255
    right_cam = np.moveaxis(observation['observation']['right_camera']['rgb'], -1, 0) / 255
    obs = dict(
        head_cam = head_cam,
        front_cam = front_cam,
        left_cam = left_cam,
        right_cam = right_cam
    )
    obs['agent_pos'] = observation['joint_action']
    return obs

def get_model(ckpt_folder, task_name):
    print('ckpt_folder: ', ckpt_folder)
    return DP(ckpt_folder)

def eval(TASK_ENV, model, observation):
    '''
    TASK_ENV: Task Environment Class, you can use this class to interact with the environment
    model: The model from 'get_model()' function
    observation: The observation about the environment 
    '''
    obs = encode_obs(observation)
    # ======== Get Action ========
    if len(model.runner.obs) == 0:
        model.update_obs(obs)    
    actions = model.get_action()

    for action in actions:
        TASK_ENV.take_action(action)
        observation = TASK_ENV.get_obs()
        obs = encode_obs(observation)
        model.update_obs(obs)

def reset_model(model):
    model.runner.reset_obs()