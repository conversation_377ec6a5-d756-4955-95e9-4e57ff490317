# modified from https://github.com/callmeray/ManiSkill-ViTac2024
import sys

from PIL import Image
import numpy as np
import sapien
import math
import yaml
import cv2
import os

from sapienipc.ipc_utils.user_utils import ipc_update_render_all
from sapienipc.ipc_utils.ipc_mesh import IPCTetMesh, IPCTriMesh
from sapienipc.ipc_component import IPCFEMComponent, IPCABDComponent, IPCPlaneComponent
from sapienipc.ipc_system import IPCSystem, IPCSystemConfig
from .._GLOBAL_CONFIGS import ASSETS_PATH, CONFIGS_PATH

import pickle
from ..utils.phong_shading import <PERSON>ong<PERSON>hading<PERSON><PERSON><PERSON>
from ..utils.transforms import *

import scipy
from scipy.ndimage import gaussian_filter
from sklearn.neighbors import NearestNeighbors
import transforms3d as t3d
from copy import deepcopy

from ..utils.ipc_create_actor import TwinActor

class TactileSensor:
    MATERIAL_DEFAULT_CONFIG = {
        'elastic_modulus': 1e5,
        'poisson_ratio': 0.3,
        'density': 1000,
        'friction': 0.5
    }
    EPS_D = 1e-4
    def __init__(self,
                 scene: sapien.Scene,
                 ipc_system: IPCSystem,
                 base:sapien.physx.PhysxArticulationLinkComponent,
                 bias_mat:np.ndarray,
                 init_mat:np.ndarray, 
                 sensor_config:dict,
                 name='tactile_sensor',
                 material_config:dict={},
                 render:bool=True,
                 render_config:dict={
                    'base_color': [0.3, 0.3, 0.3, 1.0],
                    'roughness': 0.5,
                    'metallic': 0.1
                 }):
        '''
            创建触觉传感器，仅包含一个 FEM 的传感器表面，并不会立即创建

            Args:
                scene: sapien.Scene, sapien 场景对象
                ipc_system: IPCSystem, IPC 系统对象
                base: sapien.physx.PhysxArticulationLinkComponent, 传感器的坐标基
                bias_mat: np.ndarray, 传感器的位姿偏移矩阵
                init_mat: np.ndarray, 采集偏移矩阵时坐标基的位姿矩阵
                sensor_config: dict, 传感器配置
                name: str, 传感器名称，默认为 'tactile_sensor'
                material_config: dict, 材料参数，可包含 density, elastic_modulus, poisson_ratio, friction
                render: bool, 是否渲染，默认为 True
                render_config: dict, 渲染参数，会被传递给 RenderMaterial
        '''
        self.name = name
        self.scene = scene
        self.ipc_system = ipc_system
        if self.ipc_system is None:
            self.time_step = self.scene.get_timestep()
        else:
            self.time_step = ipc_system.config.time_step

        self.base:sapien.physx.PhysxArticulationLinkComponent = base
        self.bias_mat = np.array(bias_mat)
        self.base_pose_mat = np.array(init_mat)

        self.material_config = deepcopy(self.MATERIAL_DEFAULT_CONFIG)
        self.material_config.update(material_config)

        self.res = os.path.join(ASSETS_PATH, 'tactile_sensors', sensor_config['path'])
        self.tet_mesh = IPCTetMesh(os.path.join(self.res, 'tet.msh'))
        
        self.active = np.loadtxt(
            os.path.join(self.res, 'active.txt')).astype(bool)
        self.on_surface = np.loadtxt(
            os.path.join(self.res, 'on_surface.txt')).astype(bool)
        self.faces = np.loadtxt(
            os.path.join(self.res, 'faces.txt')).astype(np.int32)
        self.boundary_idx = []
        for i in range(len(self.active)):
            if self.active[i] > 0:
                self.boundary_idx.append(i)
        self.boundary_idx = np.array(self.boundary_idx)
        boundary_num = len(self.boundary_idx)
        assert boundary_num >= 6
        self.transform_calculation_ids = [
            self.boundary_idx[0],
            self.boundary_idx[boundary_num // 6],
            self.boundary_idx[2 * boundary_num // 6],
            self.boundary_idx[3 * boundary_num // 6],
            self.boundary_idx[4 * boundary_num // 6],
            self.boundary_idx[5 * boundary_num // 6],
        ]
        
        self.render = render
        self.render_config = render_config

        self.ipc_entity = None
        self.pose = self._update_pose()
    
    def load(self, exists_ok:bool=True):
        '''
            加载触觉传感器
        '''
        if self.ipc_system is None:
            raise Exception('IPC system is None.')
        if self.ipc_entity is not None and not exists_ok:
            raise Exception('Entity has been created.')

        self.fem_component = IPCFEMComponent()
        self.fem_component.set_tet_mesh(self.tet_mesh)
        self.fem_component.set_material(
            self.material_config['density'],
            self.material_config['elastic_modulus'],
            self.material_config['poisson_ratio']
        )
        self.fem_component.set_friction(self.material_config['friction'])

        if self.render:
            self.render_component = sapien.render.RenderCudaMeshComponent(
                self.tet_mesh.n_vertices,
                self.tet_mesh.n_surface_triangles
            )
            self.render_component.set_vertex_count(self.tet_mesh.n_vertices)
            self.render_component.set_triangle_count(self.tet_mesh.n_surface_triangles)
            self.render_component.set_triangles(self.tet_mesh.surface_triangles)

            self.render_component.set_material(
                sapien.render.RenderMaterial(**self.render_config)
            )

        self.ipc_entity = sapien.Entity()
        self.ipc_entity.add_component(self.fem_component)
        if self.render:
            self.ipc_entity.add_component(self.render_component)
        self.init_pose = self.pose = self._update_pose()
        self.ipc_entity.set_pose(self.init_pose)
        self.ipc_entity.set_name(f'{self.name}_sensor')
        self.scene.add_entity(self.ipc_entity)
        
        self.vel_set = False
        self.target_pose = None
        self.target_start_pts = None
        self.target_move_list = []
        self.init_surface_vertices = self.get_surface_vertices_world()
        self.init_boundary_pts = self.get_vertices_world()[self.transform_calculation_ids, :]
    
    def remove(self, removed_ok:bool=True):
        '''
            从 scene 中移除传感器
        '''
        if self.ipc_entity is None and not removed_ok:
            raise Exception('Entity has not been created.')
        
        self.scene.remove_entity(self.ipc_entity)
        self.ipc_entity = None

    @staticmethod
    def trans_mat(to_mat:np.ndarray, from_mat:np.ndarray, scale:float=1.):
        to_rot = to_mat[:3, :3]
        from_rot = from_mat[:3, :3]
        rot_mat = to_rot @ from_rot.T

        trans_mat = (to_mat[:3, 3] - from_mat[:3, 3])/scale

        result = np.eye(4)
        result[:3, :3] = rot_mat
        result[:3, 3] = trans_mat
        result = np.where(np.abs(result) < 1e-5, 0, result)
        return result

    def base2world(self, entity_mat, scale=1.) -> sapien.Pose:
        '''将 base 坐标系下的矩阵转换到世界坐标系下'''
        entity_mat = np.array(entity_mat)
        base_mat = self.base.get_pose().to_transformation_matrix()
        p = entity_mat[:3, 3]*scale + base_mat[:3, 3]
        q_mat = entity_mat[:3, :3] @ base_mat[:3, :3]
        return sapien.Pose(p, t3d.quaternions.mat2quat(q_mat))

    def word2base(self, entity_mat, scale=1.) -> sapien.Pose:
        '''将世界坐标系下的矩阵转换到 base 坐标系下'''
        entity_mat = np.array(entity_mat)
        base_mat = self.base.get_pose().to_transformation_matrix()
        p = entity_mat[:3, 3] - base_mat[:3, 3]
        q_mat = entity_mat[:3, :3] @ base_mat[:3, :3].T
        return sapien.Pose(p, t3d.quaternions.mat2quat(q_mat))
    
    def transform_to_sensor_frame(self, input_vertices):
        '''将坐标转换到传感器坐标系'''
        current_pose_transform = np.eye(4)
        current_pose_transform[:3, :3] = t3d.quaternions.quat2mat(self.pose.q)
        current_pose_transform[:3, 3] = self.pose.p
        v_cv = transform_pts(input_vertices, np.linalg.inv(current_pose_transform))
        return v_cv

    def _update_pose(self) -> sapien.Pose:
        '''
            根据当前基的坐标位置、偏移矩阵和采集时基坐标位姿矩阵，更新传感器位姿
        '''
        new_mat = np.eye(4)
        new_base_pose_mat = self.base.get_pose().to_transformation_matrix()
        base_trans_mat = self.trans_mat(
            new_base_pose_mat,
            self.base_pose_mat
        )
        new_mat[:3, :3] = base_trans_mat[:3,:3] @ self.bias_mat[:3, :3] @ base_trans_mat[:3, :3].T
        new_mat[:3, 3] = base_trans_mat[:3, :3] @ self.bias_mat[:3, 3]
        return self.base2world(new_mat)

    def get_vertices_world(self):
        '''获取所有 FEM 面片的世界坐标'''
        if self.ipc_entity is None:
            return np.ones((self.tet_mesh.n_vertices, 3)) * np.array(self.pose.p)
        else:
            v:np.ndarray = self.fem_component.get_positions().cpu().numpy()[:, :3]
            return v.copy()

    def get_surface_vertices_world(self):
        '''获取表面 FEM 面片的世界坐标'''
        return self.get_vertices_world()[self.on_surface].copy()

    def get_surface_vertices_sensor(self):
        '''获取表面 FEM 面片相对传感器平面的坐标'''
        v = self.get_surface_vertices_world()
        v_cv = self.transform_to_sensor_frame(v)
        return v_cv

    def get_boundary_vertices_world(self):
        '''获取边界 FEM 面片的世界坐标'''
        return self.get_vertices_world()[self.boundary_idx].copy()

    def get_pose(self):
        '''获取传感器的位姿'''
        return self.pose
    
    def get_forces(self):
        '''获取传感器表面上的接触力'''
        return self.fem_component.get_collision_forces().cpu().numpy()[self.on_surface, :3], self.fem_component.get_friction_forces().cpu().numpy()[self.on_surface, :3]
    
    def debug(self):
        pass
    
    def update_pose(self):
        '''
            设置 target_pose，并返回规划步数
            Returns:
                int, 规划出的路径数
        '''
        self.target_pose = self._update_pose()
        self.start_pose = self.pose
        if self.ipc_entity is None:
            return 0
        else:
            distance = np.linalg.norm(self.target_pose.p - self.pose.p)
            return np.ceil(distance / TactileSensor.EPS_D).astype(int)

    def plan_target(self, steps:int):
        '''
            设置传感器的目标位姿，只赋值 pose，不移动软体面

            Args:
                target: sapien.Pose, 目标位姿
        '''
        assert self.target_pose is not None, f'{self.name}: Target pose is not set.'
        if self.ipc_entity is not None:
            current_mat = self.pose.to_transformation_matrix()
            target_mat = self.target_pose.to_transformation_matrix()
            R = target_mat[:3, :3] @ current_mat[:3, :3].T
            T = (target_mat[:3, 3] - current_mat[:3, 3]) * \
                np.linspace([0, 0, 0], [1, 1, 1], steps+1)

            self.target_start_pts = self.fem_component.get_positions().cpu()\
                .numpy()[self.boundary_idx].copy()
            self.target_move_list:list[np.ndarray] = []
            for s in range(1, steps+1):
                x_mat = np.eye(4)
                x_mat[:3, :3] = R
                x_mat[:3, 3] = T[s]
                self.target_move_list.append(x_mat)
        self.pose = self.target_pose

    def step(self, count:int):
        '''
            逐步移动传感器到目标位姿

            Args:
                count: int, 移动步数序号
        '''
        if self.ipc_entity is None or count >= len(self.target_move_list):
            return 
        
        trans_mat = self.target_move_list[count]
        x_next = (self.target_start_pts - self.start_pose.p) @ trans_mat[:3, :3].T \
            + trans_mat[:3, 3] + self.start_pose.p
        self.fem_component.set_kinematic_target(self.boundary_idx,  x_next)
        # print(f'step {count} done', 'from', self.fem_component.get_positions().cpu()\
        #     .numpy()[self.boundary_idx][0], 'target:', x_next[0])
            
class VisionTactileSensor(TactileSensor):
    # 间隔和偏移的单位是 mm，旋转的单位是 rad
    MARKER_DEFAULT_CONFIG = {
        'interval_range': (1., 1.),
        'rotation_range': 0.,
        'translation_range': (0., 0.),
        'pos_shift_range': (0., 0.),
        'random_noise': 0.,
        'lose_tracking_probability': 0.,
        'flow_size': 128
    }
    
    # 共用着色器，以减少初始化耗时
    phong_shading_renderer = None
    patch_array_dict = None
    @staticmethod
    def load_shader():
        cls = VisionTactileSensor
        if cls.phong_shading_renderer is None:
            cls.phong_shading_renderer = PhongShadingRenderer()
            cls.patch_array_dict = generate_patch_array(30)
            bg = cls.phong_shading_renderer.background
            bg = cv2.resize(
                bg, (960, 960))
            cls.phong_shading_renderer.background = bg
        return cls.phong_shading_renderer, cls.patch_array_dict

    def __init__(self,
                 scene: sapien.Scene,
                 ipc_system: IPCSystem,
                 base:sapien.Entity,
                 bias_mat:np.ndarray,
                 init_mat:np.ndarray,
                 sensor_config:dict,
                 marker_config:dict=None,
                 normalize:bool=False,
                 name='tactile',
                 material_config:dict={},
                 disable_list:list=[],
                 render:bool=True,
                 render_config:dict={
                    'base_color': [0.3, 0.3, 0.3, 1.0],
                    'roughness': 0.5,
                    'metallic': 0.1
                 }):
        '''
            创建视触觉传感器，包含一个 FEM 的传感器表面和一个相机

            Args:
                scene: sapien.Scene, sapien 场景对象
                ipc_system: IPCSystem, IPC 系统对象
                base: sapien.Entity, 传感器的坐标基
                bias_mat: np.ndarray, 传感器的位姿偏移矩阵
                init_mat: np.ndarray, 采集偏移矩阵时坐标基的位姿矩阵
                camera2gel: np.ndarray, 相机到传感器的变换矩阵
                sensor_config: dict, 传感器配置

                name: str, 传感器名称，默认为 'tactile'
                material_config: dict, 材料参数，可包含 density, elastic_modulus, poisson_ratio, friction
                render: bool, 是否渲染，默认为 True
                render_config: dict, 渲染参数，会被传递给 RenderMaterial
                marker_config: dict, 标记点配置
                normalize: bool, 是否归一化
                disable_list: list, 在拍照时需要禁用的渲染组件列表
        '''
        super().__init__(
            scene=scene,
            ipc_system=ipc_system,
            base=base,
            bias_mat=bias_mat,
            init_mat=init_mat,
            sensor_config=sensor_config,
            name=name,
            material_config=material_config,
            render=render,
            render_config=render_config
        )
        self.disable_list = disable_list
        self.depth_limit = sensor_config['bias'] + sensor_config['thickness']/2

        # 标记点配置
        self.marker_config = deepcopy(self.MARKER_DEFAULT_CONFIG)
        if marker_config is not None:
            self.marker_config.update(marker_config)
        self.normalize = normalize

        camera_params = np.array(sensor_config['intrinsic'])
        
        # 从相机坐标系到传感器坐标系的变换
        self.camera2gel = np.eye(4)
        self.camera2gel[:3, 3] = (0, 0, -sensor_config['bias'])
        self.gel2camera = np.linalg.inv(self.camera2gel)
        self.camera_params = camera_params
        self.camera_intrinsic = np.array(
            [[camera_params[0], 0, camera_params[2]],
                    [0, camera_params[1], camera_params[3]],
                    [0, 0, 1]],
                    dtype=np.float32
        )
        self.camera_distort_coeffs = np.array([camera_params[4], 0, 0, 0], dtype=np.float32)
        self.init_vertices_camera = self.get_vertices_camera()
        self.init_surface_vertices_camera = self.get_surface_vertices_camera()
        self.reference_surface_vertices_camera = self.get_surface_vertices_camera()

        self.camera_entity = sapien.Entity()
        self.camera = sapien.render.RenderCameraComponent(960, 960)
        self.camera.set_perspective_parameters(0.0001, 1, camera_params[0], camera_params[1], camera_params[2], camera_params[3], 0)
        self.camera_entity.add_component(self.camera)
        self.camera_entity.set_name(f'{self.name}_camera')
        self.camera_entity.set_pose(cv2ex2pose(self.get_camera_pose()))
        self.scene.add_entity(self.camera_entity)

        self.phong_shading_renderer, self.patch_array_dict = self.load_shader()
        self.force_disable = False

    def check_tactile(self):
        '''
            检测何时应加载 tactile sensor 实体，何时应删除
        '''
        if self.force_disable: return False
        if hasattr(self, 'load_tactile') and self.load_tactile:
            return True
        return False

    def load(self):
        '''
            初始化视触觉传感器
        '''
        super().load()
        self.init_vertices_camera = self.get_vertices_camera()
        self.init_surface_vertices_camera = self.get_surface_vertices_camera()
        self.reference_surface_vertices_camera = self.get_surface_vertices_camera()

    def _update_camera_pose(self):
        '''根据触觉传感器的位姿更新相机位姿'''
        self.camera_entity.set_pose(cv2ex2pose(self.get_camera_pose()))

    def update_pose(self):
        '''
            更新视触觉传感器的位姿，返回值为规划步数
        '''
        steps = 0
        if self.ipc_entity is None:
            if self.check_tactile():
                self.load()
        else:
            if not self.check_tactile():
                self.remove()
        steps = super().update_pose()
        return steps
    
    def plan_target(self, steps):
        '''
            设置传感器的目标位姿，只赋值 pose 并移动相机，不移动软体面
        '''
        super().plan_target(steps)
        self._update_camera_pose()

    def step(self, count:int):
        '''
            逐步移动传感器到目标位姿

            Args:
                count: int, 移动步数序号
        '''
        super().step(count)
        if self.ipc_entity is not None:
            if np.max(self.get_forces()[1]) > 1000:
                self.target_move_list.clear()
                return False
        return True

    def disable_render(self):
        if self.ipc_entity is not None and self.render:
            self.render_component.disable()
        for component in self.disable_list:
            if component is not None:
                component.disable()
    
    def enable_render(self):
        if self.ipc_entity is not None and self.render:
            self.render_component.enable()
        for component in self.disable_list:
            if component is not None:
                component.enable()

    def transform_to_camera_frame(self, input_vertices):
        '''将给定世界坐标系中的点转化到相机坐标系中'''
        pose = self.pose
        current_pose_transform = np.eye(4)
        current_pose_transform[:3, :3] = t3d.quaternions.quat2mat(pose.q)
        current_pose_transform[:3, 3] = pose.p
        v_cv = transform_pts(input_vertices, self.gel2camera @ np.linalg.inv(current_pose_transform))
        return v_cv

    def get_vertices_camera(self):
        '''获取所有 FEM 面片的相机坐标'''
        v = self.get_vertices_world()
        v_cv = self.transform_to_camera_frame(v)
        return v_cv

    def get_camera_pose(self):
        '''获取将给定世界坐标系中的点变换到相机坐标的变换矩阵'''
        pose = self.pose
        current_pose_transform = np.eye(4)
        current_pose_transform[:3, :3] = t3d.quaternions.quat2mat(pose.q)
        current_pose_transform[:3, 3] = pose.p
        return np.linalg.inv(self.gel2camera @ np.linalg.inv(current_pose_transform))

    def get_surface_vertices_camera(self):
        '''获取表面 FEM 面片的相机坐标'''
        v = self.get_surface_vertices_world()
        v_cv = self.transform_to_camera_frame(v)
        return v_cv

    def get_init_surface_vertices_camera(self):
        '''获取最初表面 FEM 面片的相机坐标'''
        return self.init_surface_vertices_camera.copy()

    def set_reference_surface_vertices_camera(self):
        self.reference_surface_vertices_camera = self.get_surface_vertices_camera().copy()

    def _gen_marker_grid(self):
        '''生成标记格点'''
        def rand_between(a, b, size=None)->float:
            '''在 [a, b] 范围内生成一个随机数'''
            return (b - a) * np.random.random(size) + a

        # 在给定范围内随机间隔
        itv_range = self.marker_config['interval_range']
        marker_interval = rand_between(itv_range[0], itv_range[1])
        # 在给定范围内随机旋转角度
        rot_range = self.marker_config['rotation_range']
        marker_rotation_angle = rand_between(-rot_range, rot_range)
        # 在给定范围内随机偏移
        trans_range = self.marker_config['translation_range']
        marker_translation_x = rand_between(-trans_range[0], trans_range[0])
        marker_translation_y = marker_translation_x = rand_between(-trans_range[1], trans_range[1])

        # 生成网格
        marker_x_start = -math.ceil((15 + marker_translation_x) / marker_interval) * marker_interval + marker_translation_x
        marker_x_end = math.ceil((15 - marker_translation_x) / marker_interval) * marker_interval + marker_translation_x
        marker_y_start = -math.ceil((15 + marker_translation_y) / marker_interval) * marker_interval + marker_translation_y
        marker_y_end = math.ceil((15 - marker_translation_y) / marker_interval) * marker_interval + marker_translation_y

        marker_x = np.linspace(marker_x_start, marker_x_end, round((marker_x_end-marker_x_start)/marker_interval)+1, True)
        marker_y = np.linspace(marker_y_start, marker_y_end, round((marker_y_end-marker_y_start)/marker_interval)+1, True)
        marker_xy = np.array(np.meshgrid(marker_x, marker_y)).reshape((2, -1)).T
        marker_num = marker_xy.shape[0]

        # 随机移动标记点
        sft_range = self.marker_config['pos_shift_range']
        marker_pos_shift_x = rand_between(-sft_range[0], sft_range[1], marker_num)
        marker_pos_shift_y = rand_between(-sft_range[1], sft_range[1], marker_num)
        marker_xy[:, 0] += marker_pos_shift_x
        marker_xy[:, 1] += marker_pos_shift_y

        # 旋转标记点
        rot_mat = np.array([
            [math.cos(marker_rotation_angle), -math.sin(marker_rotation_angle)],
            [math.sin(marker_rotation_angle), math.cos(marker_rotation_angle)],
        ])
        marker_rotated_xy = marker_xy @ rot_mat.T
        return marker_rotated_xy / 1000.0

    def _gen_marker_weight(self, marker_pts):
        if self.ipc_entity is None:
            return np.zeros((marker_pts.shape[0], 3), dtype=np.int32), np.ones((marker_pts.shape[0], 3))*np.array([0.33, 0.33, 0.34])

        surface_pts = self.get_init_surface_vertices_camera()[:, :2]
        marker_on_surface = in_hull(marker_pts, surface_pts)
        marker_pts = marker_pts[marker_on_surface]

        f_v_on_surface = self.on_surface[self.faces]
        f_on_surface = self.faces[np.sum(f_v_on_surface, axis=1) == 3]
        global_id_to_surface_id = np.cumsum(self.on_surface) - 1
        f_on_surface_on_surface_id = global_id_to_surface_id[f_on_surface]
        f_center_on_surface = np.mean(self.init_vertices_camera[f_on_surface][:, :, :2], axis=1)

        nbrs = NearestNeighbors(n_neighbors=4, algorithm="ball_tree").fit(f_center_on_surface)
        distances, idx = nbrs.kneighbors(marker_pts)

        marker_pts_surface_idx = []
        marker_pts_surface_weight = []
        valid_marker_idx = []

        for i in range(marker_pts.shape[0]):
            possible_face_ids = idx[i]
            p = marker_pts[i]
            for possible_face_id in possible_face_ids.tolist():
                face_vertices_idx = f_on_surface_on_surface_id[possible_face_id]
                closet_pts = surface_pts[face_vertices_idx][:, :2]
                p0, p1, p2 = closet_pts
                A = np.stack([p1 - p0, p2 - p0], axis=1)
                w12 = np.linalg.inv(A) @ (p - p0)
                if possible_face_id == possible_face_ids[0]:
                    marker_pts_surface_idx.append(face_vertices_idx)
                    marker_pts_surface_weight.append(np.array([1 - w12.sum(), w12[0], w12[1]]))
                    valid_marker_idx.append(i)
                    if w12[0] >= 0 and w12[1] >= 0 and w12[0] + w12[1] <= 1:
                        break
                elif w12[0] >= 0 and w12[1] >= 0 and w12[0] + w12[1] <= 1:
                    marker_pts_surface_idx[-1] = face_vertices_idx
                    marker_pts_surface_weight[-1] = np.array([1 - w12.sum(), w12[0], w12[1]])
                    valid_marker_idx[-1] = i
                    break

        valid_marker_idx = np.array(valid_marker_idx).astype(np.int32)
        marker_pts = marker_pts[valid_marker_idx]
        marker_pts_surface_idx = np.stack(marker_pts_surface_idx)
        marker_pts_surface_weight = np.stack(marker_pts_surface_weight)
        assert np.allclose(
            (surface_pts[marker_pts_surface_idx] * marker_pts_surface_weight[..., None]).sum(1), marker_pts
        ), f"max err: {np.abs((surface_pts[marker_pts_surface_idx] * marker_pts_surface_weight[..., None]).sum(1) - marker_pts).max()}"

        return marker_pts_surface_idx, marker_pts_surface_weight

    def gen_marker_uv(self, marker_pts):
        marker_uv = cv2.projectPoints(marker_pts, np.zeros(3, dtype=np.float32), np.zeros(3, dtype=np.float32),
                                      self.camera_intrinsic,
                                      self.camera_distort_coeffs)[0].squeeze(1)

        return marker_uv

    def gen_marker_flow(self):
        marker_grid = self._gen_marker_grid()
        marker_pts_surface_idx, marker_pts_surface_weight = self._gen_marker_weight(marker_grid)
        init_marker_pts = (self.reference_surface_vertices_camera[marker_pts_surface_idx] * marker_pts_surface_weight[
            ..., None]).sum(1)
        curr_marker_pts = (self.get_surface_vertices_camera()[marker_pts_surface_idx] * marker_pts_surface_weight[
            ..., None]).sum(1)

        init_marker_uv = self.gen_marker_uv(init_marker_pts)
        curr_marker_uv = self.gen_marker_uv(curr_marker_pts)
        marker_mask = np.logical_and.reduce(
            [
                init_marker_uv[:, 0] > 5,
                init_marker_uv[:, 0] < 955,
                init_marker_uv[:, 1] > 5,
                init_marker_uv[:, 1] < 955,
            ]
        )
        marker_flow = np.stack([init_marker_uv, curr_marker_uv], axis=0)
        marker_flow = marker_flow[:, marker_mask]

        # post processing
        lose_probability = self.marker_config['lose_tracking_probability']
        no_lose_tracking_mask = np.random.rand(marker_flow.shape[1]) > lose_probability
        marker_flow = marker_flow[:, no_lose_tracking_mask, :]
        random_noise = self.marker_config['random_noise']
        noise = np.random.randn(*marker_flow.shape) * random_noise
        marker_flow += noise

        original_point_num = marker_flow.shape[1]

        flow_size = self.marker_config['flow_size']
        if original_point_num >= flow_size:
            chosen = np.random.choice(original_point_num, flow_size, replace=False)
            ret = marker_flow[:, chosen, ...]
        else:
            ret = np.zeros((marker_flow.shape[0], flow_size, marker_flow.shape[-1]))
            ret[:, :original_point_num, :] = marker_flow.copy()
            ret[:, original_point_num:, :] = ret[:, original_point_num - 1: original_point_num, :]

        if self.normalize:
            ret /= 160.0
            ret -= 1.0
        return ret
    
    def update_picture(self):
        self.disable_render()
        self._update_camera_pose()
        ipc_update_render_all(self.scene)
        self.scene.update_render()
        self.camera.take_picture()
        self.enable_render()

    def _gen_depth(self):
        '''获取接触表面的深度'''

        position = self.camera.get_picture('Position')
        depth = - position[:, :, 2]
        depth = np.where(depth > self.depth_limit, self.depth_limit, depth)
         
        fem_smooth_sigma = 2
        depth = gaussian_filter(depth, fem_smooth_sigma)
        
        return depth

    def gen_rgb_image(self):
        '''获取当前视触觉传感器相机的显示'''

        depth = self._gen_depth()
        rgb = VisionTactileSensor.phong_shading_renderer.generate(depth)
        rgb = rgb.astype(np.float64)

        marker_grid = self._gen_marker_grid()
        marker_pts_surface_idx, marker_pts_surface_weight = self._gen_marker_weight(marker_grid)

        curr_marker_pts = (self.get_surface_vertices_camera()[marker_pts_surface_idx] * marker_pts_surface_weight[
            ..., None]).sum(1)
        curr_marker_uv = self.gen_marker_uv(curr_marker_pts)
        curr_marker = self.draw_marker(
            marker_uv=curr_marker_uv, img_w=960, img_h=960
        )
        rgb = rgb.astype(np.float64)
        rgb *= np.dstack([curr_marker.astype(np.float64) / 255] * 3)
        rgb = rgb.astype(np.uint8)
        return rgb

    def draw_marker(self, marker_uv, marker_size=3, img_w=960, img_h=960):
        '''获取标记点的图像'''

        marker_uv_compensated = marker_uv + np.array([0.5, 0.5])
        marker_image = np.ones((img_h + 24, img_w + 24), dtype=np.uint8) * 255
        for i in range(marker_uv_compensated.shape[0]):
            uv = marker_uv_compensated[i]
            u = uv[0] + 12
            v = uv[1] + 12
            patch_id_u = math.floor((u - math.floor(u)) * self.patch_array_dict["super_resolution_ratio"])
            patch_id_v = math.floor((v - math.floor(v)) * self.patch_array_dict["super_resolution_ratio"])
            patch_id_w = math.floor((marker_size - self.patch_array_dict["base_circle_radius"]) * self.patch_array_dict[
                "super_resolution_ratio"])
            current_patch = self.patch_array_dict["patch_array"][patch_id_u, patch_id_v, patch_id_w]
            patch_coord_u = math.floor(u) - 6
            patch_coord_v = math.floor(v) - 6
            if marker_image.shape[1] - 12 > patch_coord_u >= 0 and marker_image.shape[0] - 12 > patch_coord_v >= 0:
                old_status = marker_image[patch_coord_v:patch_coord_v + 12, patch_coord_u:patch_coord_u + 12]
                new_status = np.where(current_patch <= 50, current_patch, old_status)
                marker_image[patch_coord_v:patch_coord_v + 12, patch_coord_u:patch_coord_u + 12] = new_status
        marker_image = marker_image[12:-12, 12:-12]
        return marker_image
    
    def debug_info(self):
        '''获取调试信息，包括 rgb, raw, depth, flow'''
        rgba = self.camera.get_picture('Color')
        position = self.camera.get_picture('Position')
        raw = (rgba * 255).clip(0, 255).astype("uint8")
        rgb = self.gen_rgb_image()
        info = {
            'rgb'  : rgb,
            'raw'   : raw,
            'depth' : position[:, :, 2:],
            'flow'  : self.gen_marker_flow()
        }
        return info

    def debug(self):
        import pickle
        if hasattr(self, 'counter'):
            self.counter += 1
        else:
            self.counter = 0
        save_dir = f'{self.name}_debug'
        save_dir = os.path.join(save_dir, self.name)
        os.makedirs(save_dir, exist_ok=True)

        save_data = self.debug_info()
        pickle.dump(
            save_data,
            open(os.path.join(save_dir, f"{self.counter:05d}.pkl"), "wb")
        )

class VisionTactileSensors:
    '''
        视触觉传感器的集合，用于管理机械臂的所有视触觉传感器
    '''
    def __init__(self, **kwargs):
        self.sensors:dict[str, VisionTactileSensor] = {}
        self.sensors_config = deepcopy(kwargs['left_embodiment_config']\
            .get('vision_tactile_sensor_list', []))
        self.sensors_type_config = yaml.load(
            open(os.path.join(CONFIGS_PATH, '_tactile_sensor_config.yml'), 'r', encoding='utf-8'),
            Loader=yaml.FullLoader
        )

    def load_sensor(self,
                    scene:sapien.Scene,
                    ipc_system:IPCSystem,
                    links:list[sapien.physx.PhysxArticulationLinkComponent]):
        self.scene = scene
        self.ipc_system = ipc_system
        # convert base link name to link component
        for link in links:
            for sensor_config in self.sensors_config:
                if sensor_config['base_link'] == link.get_name():
                    sensor_config['base_link'] = link

        # convert disable entities name to render component
        render_dict:dict[str, sapien.render.RenderBodyComponent] = {}
        for sensor_config in self.sensors_config:
            for entity_name in sensor_config.get('disable_entities', []):
                if entity_name not in render_dict:
                    render_dict.update({entity_name: None})
        for entity in self.scene.get_entities():
            name = entity.get_name()
            if name in render_dict:
                render_dict[name] = entity.find_component_by_type(sapien.render.RenderBodyComponent)
        for sensor_config in self.sensors_config:
            render_list = []
            for entity_name in sensor_config.get('disable_entities', []):
                render_list.append(render_dict[entity_name])
            sensor_config['disable_entities'] = render_list
        # load sensors
        for sensor_config in self.sensors_config:
            self.add_sensor(**sensor_config)

    def add_sensor(self,
                   base_link,
                   bias_mat:np.ndarray,
                   init_mat:np.ndarray,
                   name:str='tactile',
                   type:str='pika',
                   disable_entities:list=[]):
        self.sensors[name] = VisionTactileSensor(
            scene=self.scene,
            ipc_system=self.ipc_system,
            base=base_link,
            bias_mat=bias_mat,
            init_mat=init_mat,
            name=name,
            sensor_config=self.sensors_type_config[type],
            disable_list=disable_entities
        )
    
    def update_sensors(self):
        '''
            update all sensors' pose.
            Workflow:
                1. update_pose(): calculate target pose and steps
                2. plan_target(): use the max steps to plan the path
                3. step():        step all sensors

            return: bool, whether any sensor exists
        '''
        max_steps = 0
        for sensor in self.sensors.values():
            max_steps = max(max_steps, sensor.update_pose())
        for sensor in self.sensors.values():
            sensor.plan_target(max_steps)
        is_fail = False
        if max_steps > 0:
            for s in range(max_steps):
                for sensor in self.sensors.values():
                    is_fail |= not sensor.step(s)
                TwinActor.step_all('b')
                self.ipc_system.step()
                TwinActor.step_all('a')
        return {
            'status': 'success' if not is_fail else 'fail',
            'loaded': max_steps != 0
        }
    
    def update_picture(self):
        '''
            update all sensors' picture
        '''
        for sensor in self.sensors.values():
            sensor.update_picture()
    
    def set_tactile_status(self, active:bool=False, name_list:list=None):
        if name_list is None:
            name_list = self.sensors.keys()
        for name in name_list:
            self.sensors[name].load_tactile = active
    
    def set_force_disable(self, active:bool=False, name_list:list=None):
        if name_list is None:
            name_list = self.sensors.keys()
        for name in name_list:
            self.sensors[name].force_disable = active
    
    def get_config(self) -> dict[str, dict]:
        res = {}
        for name in self.sensors.keys():
            res[name] = {}
        return res

    def get_rgb(self) -> dict[str, np.ndarray]:
        res = {}
        for name, sensor in self.sensors.items():
            res[name] = {'rgb': sensor.gen_rgb_image()}
        res['ll_tactile']['rgb'] = np.array(Image.fromarray(res['ll_tactile']['rgb']).rotate(180))
        res['rl_tactile']['rgb'] = np.array(Image.fromarray(res['rl_tactile']['rgb']).rotate(180))
        return res

    def get_markder_flow(self) -> dict[str, np.ndarray]:
        res = {}
        for name, sensor in self.sensors.items():
            res[name] = {'flow': sensor.gen_marker_flow()}
        return res
    
    def get_depth(self) -> dict[str, np.ndarray]:
        res = {}
        for name, sensor in self.sensors.items():
            res[name] = {'depth': sensor._gen_depth()}
        return res
    
    def get_debug(self) -> dict:
        res = {}
        for name, sensor in self.sensors.items():
            res[name] = {'debug': sensor.debug_info()}
        return res

    def get_all(self) -> dict:
        res = {}
        for name, sensor in self.sensors.items():
            res[name] = {
                'rgb': sensor.gen_rgb_image(),
                'flow': sensor.gen_marker_flow(),
                'depth': sensor._gen_depth()
            }
        return res