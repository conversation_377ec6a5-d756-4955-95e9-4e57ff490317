from .base_task import Base_task
from .utils import *
import sapien

class classify_tactile(Base_task):

    def setup_demo(self,**kwags):
        super()._init(**kwags)
        self.create_table_and_wall()
        self.load_robot(**kwags)
        self.load_camera(**kwags)
        self.pre_move()
        self.load_actors()
        if self.messy_table:
            self.get_messy_table()
        self.step_lim = 300


    def pre_move(self):
        render_freq = self.render_freq
        self.render_freq=0
        self.vsensors.set_force_disable(True)
        self.together_open_gripper(left_pos=1, right_pos=1, save_freq=None)
        self.vsensors.set_force_disable(False)
        self.vsensors.set_tactile_status(True)
        self.render_freq = render_freq

    def load_actors(self):
        self.base_scale = 1.
        
        self.lr = -1 if np.random.random_integers(0, 1) == 0 else 1
        self.tag = np.random.random_integers(0, 1)
        
        self.coaster_A = create_box(
            scene=self.scene,
            pose=sapien.Pose([0.025+self.lr*-0.085, -0.035, 0.743], [0, 1, 0, 0]),
            half_size=(0.03, 0.03, 0.002),
            color=(0.5, 0.2, 0.2),
            is_static=True
        )
        
        self.coaster_B = create_box(
            scene=self.scene,
            pose=sapien.Pose([0.025+self.lr*-0.085, 0.085, 0.743], [0, 1, 0, 0]),
            half_size=(0.03, 0.03, 0.002),
            color=(0.2, 0.5, 0.2),
            is_static=True
        )
        
        if self.tag == 0:
            self.box = create_box(
                scene=self.scene,
                pose = sapien.Pose([self.lr*0.11, 0.037, 0.782], [1, 0, 0, 0]),
                half_size=(0.02, 0.02, 0.04),
                color=(0, 0, 0),
                name='box',
            )
            self.base = self.coaster_A
            self.obj = self.box
        else:
            self.prism, _ = create_actor(
                scene=self.scene,
                pose = sapien.Pose([self.lr*0.11, 0.037, 0.782], [1, 0, 0, 0]),
                modelname="prism",
                convex=False,
                scale=(0.02, 0.02, 0.01)
            )
            self.base = self.coaster_B
            self.obj = self.prism

    def play_once(self):
        if self.lr == -1:
            self.init_base_mat = np.array([[1.0, 0.0, 0.0, 0.10000000149011612], [0.0, -1.0, 0.0, 0.08500000089406967], [0.0, 0.0, -1.0, 0.7429999709129333], [0.0, 0.0, 0.0, 1.0]])
            seq = [
                {
                    'd': 'load tactile sensor',
                    'load': ['lr_tactile', 'll_tactile']
                },
                {
                    'd': 'move left arm to position',
                    'lb': [[0.9998190474793415, 0.019022941347284886, -6.118244568807804e-18, -0.34984779357910156], [0.019022941347284886, -0.9998190474793415, 3.215663311659299e-16, -0.05009278655052185], [-0.0, -3.216245299353273e-16, -1.0, 0.0487024188041687], [0.0, 0.0, 0.0, 1.0]] if self.tag == 1 else [[0.9997546571016801, 0.021815554699495667, 0.0038349936969240468, -0.3563106954097748], [0.02181472422106409, -0.999761997233272, 0.00025825432845633784, 0.06910526007413864], [0.0038397149192426145, -0.00017453163770297353, -0.9999926130367396, 0.041237592697143555], [0.0, 0.0, 0.0, 1.0]],
                },
                {
                    'd': 'close left gripper',
                    'cl': 0.285 if self.tag == 1 else 0.298
                },
                {
                    'd': 'move left arm to position',
                    'lb': [[0.9996059491964704, 0.027054534676509513, 0.007483213511665562, -0.14453765749931335], [0.027048542059646654, -0.9996337153143126, 0.0009008769901935021, -0.010111264884471893], [0.0075048453329269686, -0.0006981119834800328, -0.9999715945646593, 0.07632690668106079], [0.0, 0.0, 0.0, 1.0]] if self.tag == 1 else [[0.9994919307568099, 0.031428519363500676, 0.005303632966433335, -0.1442917138338089], [0.031410299324146206, -0.9995005011724519, 0.0034844299942684144, -0.001253705471754074], [0.005410494283530485, -0.0033160709635771963, -0.9999798649097753, 0.09030866622924805], [0.0, 0.0, 0.0, 1.0]], 
                },
                {
                    'd': 'open left gripper',
                    'cl': 0.5
                },
            ]
        else:
            self.init_base_mat = np.array([[1.0, 0.0, 0.0, -0.05999999865889549], [0.0, -1.0, 0.0, 0.08500000089406967], [0.0, 0.0, -1.0, 0.7429999709129333], [0.0, 0.0, 0.0, 1.0]] if self.tag == 1 else [[1.0, 0.0, 0.0, -0.05999999865889549], [0.0, -1.0, 0.0, -0.03500000014901161], [0.0, 0.0, -1.0, 0.7429999709129333], [0.0, 0.0, 0.0, 1.0]])
            seq = [
                {
                    'd': 'load tactile sensor',
                    'load': ['rr_tactile', 'rl_tactile']
                },
                {
                    'd': 'move right arm to position',
                    'rb': [[-0.9999995583047893, -0.0008738172606389347, 0.00034616993129708623, 0.3118121325969696], [-0.0008726644620694556, 0.9999941198808069, 0.0033164228540743723, -0.05132322013378143], [-0.00034906584331009674, 0.003316119299029401, -0.999994440737463, 0.03333920240402222], [0.0, 0.0, 0.0, 1.0]] if self.tag == 1 else [[-0.9999451693655121, -0.010471784116245709, 3.367982643971753e-18, 0.3003067076206207], [-0.010471784116245709, 0.9999451693655121, -3.2160689505828413e-16, 0.0742141604423523], [-0.0, -3.216245299353273e-16, -1.0, 0.040038108825683594], [0.0, 0.0, 0.0, 1.0]],
                },
                {   
                    'd': 'close right gripper',
                    'cr': 0.28 if self.tag == 1 else 0.298
                },
                {
                    'd': 'move right arm to position',
                    'rb': [[-0.9999995583047893, -0.0008738172606389347, 0.00034616993129708623, 0.1440720409154892], [-0.0008726644620694556, 0.9999941198808069, 0.0033164228540743723, -0.0024802014231681824], [-0.00034906584331009674, 0.003316119299029401, -0.999994440737463, 0.07285326719284058], [0.0, 0.0, 0.0, 1.0]] if self.tag == 1 else [[-0.9999837639647247, -0.0050664500061367145, 0.0026082352802624196, 0.1386951208114624], [-0.005061415874563623, 0.9999853225139591, 0.0019330871676674045, 0.006644677370786667], [-0.002617990887417994, 0.0019198544185438432, -0.9999947301274767, 0.08233237266540527], [0.0, 0.0, 0.0, 1.0]],
                },
                {
                    'd': 'open right gripper',
                    'cr': 0.5
                },
            ]

        for i in range(len(seq)):
            s:dict = seq[i]
            
            lp, rp = None, None
            if s.get('lb'):
                init_base_mat = s.get('base', self.init_base_mat)
                lp = Point.pose2list(Point.trans_base(self.base.get_pose().to_transformation_matrix(), init_base_mat, s['lb'], self.base_scale))
            elif s.get('le'):
                lp = eval(s['le'])
            
            if s.get('rb'):
                init_base_mat = s.get('base', self.init_base_mat)
                rp = Point.pose2list(Point.trans_base(self.base.get_pose().to_transformation_matrix(), init_base_mat, s['rb'], self.base_scale))
            elif s.get('re'):
                rp = eval(s['re'])
            
            if lp is not None and rp is not None:
                self.together_move_to_pose(lp, rp)
            else:
                if lp is not None:
                    self.left_move_to_pose(lp)
                if rp is not None:
                    self.right_move_to_pose(rp)
            
            if s.get('cl') and s.get('cr'):
                self.together_close_gripper(left_pos=s['cl'], right_pos=s['cr'])
            else:
                if s.get('cl'):
                    self.close_left_gripper(pos=s['cl'])
                if s.get('cr'):
                    self.close_right_gripper(pos=s['cr'])
    
    def stage_reward(self):
        var = 0.03
        center = self.base.get_pose().p
        obj_pose = self.obj.get_pose().p
        v1 = self.obj.get_pose().to_transformation_matrix()[:3, :3] @ np.array([0, 0, 1])
        cos = np.dot(v1, np.array([0, 0, 1])) / np.linalg.norm(v1)
        return not self.ipc_fail and center[0]-var <= obj_pose[0] <= center[0]+var and center[1]-var <= obj_pose[1] <= center[1]+var and obj_pose[2] <= 0.8 and cos > 0.9

    def check_success(self):
        var = 0.03
        center = self.base.get_pose().p
        obj_pose = self.obj.get_pose().p
        v1 = self.obj.get_pose().to_transformation_matrix()[:3, :3] @ np.array([0, 0, 1])
        cos = np.dot(v1, np.array([0, 0, 1])) / np.linalg.norm(v1)
        return not self.ipc_fail and center[0]-var <= obj_pose[0] <= center[0]+var and center[1]-var <= obj_pose[1] <= center[1]+var and obj_pose[2] <= 0.8 and cos > 0.9
    
    def close(self):
        if hasattr(self, 'ipc_system'):
            for entity in self.scene.get_entities():
                if 'tactile' in entity.get_name():
                    entity.remove_from_scene()
            TwinActor.clear()
            self.ipc_system.components.clear()
            self.ipc_system.rebuild()
        super().close()