
from .base_task import Base_task
from .utils import *
import math
import sapien

class dual_shoes_place(Base_task):
    def setup_demo(self,is_test = False, **kwags):
        super()._init(**kwags)
        self.create_table_and_wall(table_height=0.65)
        self.load_robot(**kwags)
        self.load_camera(**kwags)
        self.robot.move_to_homestate()
        self.pre_move()
        self.robot.set_origin_endpose()
        if is_test:
            self.id_list = [2*i+1 for i in range(5)]
        else:
            self.id_list = [2*i for i in range(5)]
        self.load_actors()
        
        self.step_lim = 600
    
    def pre_move(self):
        render_freq = self.render_freq
        self.render_freq=0
        self.together_open_gripper(save_freq=None)
        self.render_freq = render_freq

    def load_actors(self):
        self.shoe_box, self.shoe_box_data = create_actor(
            self.scene,
            pose = sapien.Pose([0,-0.13,0.597305],[0.5,0.5,-0.5,-0.5]),
            modelname="shoe_box",
            convex=False,
            is_static=True,
            z_val_protect=False
        )

        shoe_id = np.random.choice(self.id_list)

        # left shoe
        shoes_pose = rand_pose(
            xlim=[-0.3, -0.2],
            ylim=[-0.1,0.05],
            zlim=[0.7],
            ylim_prop=True,
            rotate_rand=True,
            rotate_lim=[0,1.57,0],
            qpos=[0.707,0.707,0,0]
        )

        while np.sum(pow(shoes_pose.get_p()[:2] - np.zeros(2),2)) < 0.0225:
            shoes_pose = rand_pose(
                xlim=[-0.3, -0.2],
                ylim=[-0.1,0.05],
                zlim=[0.7],
                ylim_prop=True,
                rotate_rand=True,
                rotate_lim=[0,1.57,0],
                qpos=[0.707,0.707,0,0]
            )
        

        self.left_shoe, self.left_shoe_data = create_glb(
            self.scene,
            pose=shoes_pose,
            modelname="041_shoes",
            convex=True,
            model_id = shoe_id,
            z_val_protect = True
        )


        # right shoe
        shoes_pose = rand_pose(
            xlim=[0.2,0.3],
            ylim=[-0.1,0.05],
            zlim=[0.7],
            ylim_prop=True,
            rotate_rand=True,
            rotate_lim=[0,1.57,0],
            qpos=[0.707,0.707,0,0]
        )

        while np.sum(pow(shoes_pose.get_p()[:2] - np.zeros(2),2)) < 0.0225:
            shoes_pose = rand_pose(
                xlim=[0.2,0.3],
                ylim=[-0.1,0.05],
                zlim=[0.7],
                ylim_prop=True,
                rotate_rand=True,
                rotate_lim=[0,1.57,0],
                qpos=[0.707,0.707,0,0]
            )

        self.right_shoe, self.right_shoe_data = create_glb(
            self.scene,
            pose=shoes_pose,
            modelname="041_shoes",
            convex=True,
            model_id = shoe_id,
            z_val_protect = True
        )

        self.left_shoe.find_component_by_type(sapien.physx.PhysxRigidDynamicComponent).mass = 0.1
        self.right_shoe.find_component_by_type(sapien.physx.PhysxRigidDynamicComponent).mass = 0.1
        
        self.delay(4)
        pose = sapien.core.pysapien.Entity.get_pose(self.left_shoe).p.tolist()
        pose.append(0.12)
        self.size_dict.append(pose)
        pose = sapien.core.pysapien.Entity.get_pose(self.right_shoe).p.tolist()
        pose.append(0.12)
        self.size_dict.append(pose)
        self.prohibited_area.append([-0.15,-0.25,0.15,0.01])
        
    def get_target_grap_pose(self,shoe_rpy):
        if math.fmod(math.fmod(shoe_rpy[2] + shoe_rpy[0], 2 * math.pi) + 2 * math.pi, 2*math.pi) < math.pi:
            grasp_matrix = np.array([[-1,0,0,0],[0,1,0,0],[0,0,-1,0],[0,0,0,1]])
            target_quat = [-0.707,0,-0.707,0]
        else:
            grasp_matrix = np.eye(4)
            target_quat = [0,0.707,0,-0.707]
        return grasp_matrix, target_quat

    def play_once(self):
        def choice_grasp_pose(shoe, shoe_data, pre_dis):
            pose0 = self.get_grasp_pose_w_labeled_direction(shoe,shoe_data,pre_dis=pre_dis, contact_point_id=0)
            pose1 = self.get_grasp_pose_w_labeled_direction(shoe,shoe_data,pre_dis=pre_dis, contact_point_id=1)
            if pose0[3] * pose0[-1] < 0:
                return pose1
            else:
                return pose0
        # use right arm move
        left_pose1 = choice_grasp_pose(self.left_shoe, self.left_shoe_data, pre_dis=0.06)
        right_pose1 = choice_grasp_pose(self.right_shoe, self.right_shoe_data, pre_dis=0.06)
        self.together_move_to_pose(left_target_pose=left_pose1, right_target_pose = right_pose1)
        left_pose1 = choice_grasp_pose(self.left_shoe, self.left_shoe_data, pre_dis=0.)
        right_pose1 = choice_grasp_pose(self.right_shoe, self.right_shoe_data, pre_dis=0.)
        self.together_move_to_pose(left_target_pose=left_pose1, right_target_pose = right_pose1)
        self.together_close_gripper()
        left_pose1[2] += 0.15
        right_pose1[2] += 0.15
        self.together_move_to_pose(left_target_pose=left_pose1, right_target_pose = right_pose1)

        point0 = self.get_actor_functional_pose(self.shoe_box, self.shoe_box_data, 0)
        point1 = self.get_actor_functional_pose(self.shoe_box, self.shoe_box_data, 1)
        left_pre_place_pose = self.get_grasp_pose_from_goal_point_and_direction(self.left_shoe, self.left_shoe_data, endpose_tag="left", actor_functional_point_id=0, target_point=point0, target_approach_direction=[0,0.707,-0.707,0], pre_dis=0.01)
        left_target_place_pose = self.get_grasp_pose_from_goal_point_and_direction(self.left_shoe, self.left_shoe_data, endpose_tag="left", actor_functional_point_id=0, target_point=point0, target_approach_direction=[0,0.707,-0.707,0], pre_dis=0)
        right_pre_place_pose = self.get_grasp_pose_from_goal_point_and_direction(self.right_shoe, self.right_shoe_data, endpose_tag="right", actor_functional_point_id=0, target_point=point1, target_approach_direction=[0,0.707,-0.707,0], pre_dis=0.01)
        right_target_place_pose = self.get_grasp_pose_from_goal_point_and_direction(self.right_shoe, self.right_shoe_data, endpose_tag="right", actor_functional_point_id=0, target_point=point1, target_approach_direction=[0,0.707,-0.707,0], pre_dis=0)

        right_temp_pose = [0.3,-0.07,right_pose1[2]] + right_target_place_pose[-4:]

        self.together_move_to_pose(left_target_pose = left_pre_place_pose, right_target_pose=right_temp_pose)
        self.left_move_to_pose(pose = left_target_place_pose)
        self.open_left_gripper()
        self.left_move_to_pose(pose = left_pre_place_pose)

        left_pre_place_pose[0] = -0.25
        self.together_move_to_pose(left_target_pose=left_pre_place_pose, right_target_pose = right_pre_place_pose)
        self.together_move_to_pose(left_target_pose=left_pre_place_pose, right_target_pose = right_target_place_pose)
        self.open_right_gripper()
            
        info = dict()
        info['messy_table_info'] = self.record_messy_objects
        info['texture_info'] = {'wall_texture': self.wall_texture, 'table_texture': self.table_texture}
        return info

    def stage_reward(self):
        left_shoe_pose_p = np.array(self.left_shoe.get_pose().p)
        left_shoe_pose_q = np.array(self.left_shoe.get_pose().q)
        right_shoe_pose_p = np.array(self.right_shoe.get_pose().p)
        right_shoe_pose_q = np.array(self.right_shoe.get_pose().q)
        if left_shoe_pose_q[0] < 0:
            left_shoe_pose_q *= -1
        if right_shoe_pose_q[0] < 0:
            right_shoe_pose_q *= -1
        target_pose_p = np.array([0,-0.13])
        target_pose_q = np.array([0.5,0.5,-0.5,-0.5])
        eps = np.array([0.05,0.02,0.05,0.05,0.05,0.05])
        succ_shoe_num = 0
        if np.all(abs(left_shoe_pose_p[:2] - (target_pose_p - [0,0.06])) < eps[:2]) and np.all(abs(left_shoe_pose_q - target_pose_q) < eps[-4:]) and self.is_left_gripper_open():
            succ_shoe_num += 1
        if np.all(abs(right_shoe_pose_p[:2] - (target_pose_p + [0,0.06])) < eps[:2]) and np.all(abs(right_shoe_pose_q - target_pose_q) < eps[-4:]) and self.is_right_gripper_open():
            succ_shoe_num += 1
        if succ_shoe_num == 1:
            return 0.3
        if succ_shoe_num == 2:
            return 1
        return 0
    
    def check_success(self):
        left_shoe_pose_p = np.array(self.left_shoe.get_pose().p)
        left_shoe_pose_q = np.array(self.left_shoe.get_pose().q)
        right_shoe_pose_p = np.array(self.right_shoe.get_pose().p)
        right_shoe_pose_q = np.array(self.right_shoe.get_pose().q)
        if left_shoe_pose_q[0] < 0:
            left_shoe_pose_q *= -1
        if right_shoe_pose_q[0] < 0:
            right_shoe_pose_q *= -1
        target_pose_p = np.array([0,-0.13])
        target_pose_q = np.array([0.5,0.5,-0.5,-0.5])
        eps = np.array([0.05,0.02,0.05,0.05,0.05,0.05])
        return np.all(abs(left_shoe_pose_p[:2] - (target_pose_p - [0,0.06])) < eps[:2]) and np.all(abs(left_shoe_pose_q - target_pose_q) < eps[-4:]) and \
               np.all(abs(right_shoe_pose_p[:2] - (target_pose_p + [0,0.06])) < eps[:2]) and np.all(abs(right_shoe_pose_q - target_pose_q) < eps[-4:]) and self.is_left_gripper_open() and self.is_right_gripper_open()
