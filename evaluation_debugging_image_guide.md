# Evaluation Debugging Image Guide

This evaluation debugging image is used to test whether your policy meets submission requirements and can successfully complete the evaluation process.

## Image Name

petercong/robotwin-challenge-2025-evaluation:0329

The policy directory must contain a checkpoints directory with one or more subdirectories, each representing a checkpoint instance.
The code implementation should follow the competition requirements and implement the necessary entry functions.

## Verifying Policy Usability

### Starting the Container

```bash
docker run -it --gpus 'device=0' \ 
  -v /path/to/your/policy:/workspace/policy/custom_policy \ 
  petercong/robotwin-challenge-2025-evaluation:0329 bash
```

Replace `/path/to/your/policy` with your actual policy path.
Replace `'device=0'` with the GPU device you want to use. A 4090 GPU is recommended.

### Installing Policy Dependencies

In the container, navigate to the `/workspace` directory.

If your policy dependencies are specified in a requirement.txt file, run:

```bash
pip install -r policy/custom_policy/requirement.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
```

If your policy dependencies are specified in a pyproject.toml file, run:

```bash
pip install -e /workspace/policy/custom_policy -i https://pypi.tuna.tsinghua.edu.cn/simple
```

### Starting the Evaluation Task

Note that the evaluation data (seed) used in this image differs from the actual evaluation data, so results may vary slightly from the real evaluation.

```bash
bash eval_policy.sh custom_policy empty_cup_place empty_cup_place_D435_50_0 0
```

In the command above:

- Replace `empty_cup_place` with the actual task you want to test (one of: blocks_stack_hard, bowls_stack, dual_shoes_place, put_bottles_dustbin, empty_cup_place, classify_tactile).
- Replace `empty_cup_place_D435_50_0` with the directory containing the checkpoint you want to use from the policy/checkpoints directory.

If the execution completes without errors, your policy meets the submission requirements and can successfully run through the evaluation process.
