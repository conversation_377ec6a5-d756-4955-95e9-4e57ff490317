# Copyright (c) Facebook, Inc. and its affiliates.

# This source code is licensed under the MIT license found in the
# LICENSE file in the root directory of this source tree.

sensor:
  light_sources:
    light0:
      position: [1, 0, 0.344]
      color: [0, 155, 0]
      kd: 0.6
      ks: 0.1  # green, right
    light1:
      position: [0, -1, 0.344]
      color: [0, 0, 155]
      kd: 0.6
      ks: 0.1  # red, up
    light2:
      position: [0, 1, 0.344]
      color: [155, 0, 0]
      kd: 0.6
      ks: 0.1  # blue, bottom

  with_background: True
  background_img: "bg.png"
  ka: 1
  px2m_ratio: 6e-5
  elastomer_thickness: 0.004
  max_depth: 0.024
  enable_depth_texture: False
  enable_shadow: False

  noise: # Gaussian noise calibrated on output [0, 255]
    color:
      mean: 0 
      std: 4
