import sapien.core as sapien
import numpy as np
import transforms3d as t3d
import sapien.physx as sapienp
import json
import os

import warp as wp
from .transforms import estimate_rigid_transform, quat_product
from sapienipc.ipc_utils.ipc_mesh import IPCTriMesh, IPCTetMesh
from sapienipc.ipc_utils.user_utils import ipc_update_render_all
from sapienipc.ipc_system import IPCSystem, IPCSystemConfig
from sapien.render import RenderBodyComponent, RenderShape
from sapienipc.ipc_component import IPCABDComponent, IPCPlaneComponent
from typing import Dict, Any

class TwinActor:
    '''
        An actor in both PhysX system and IPC system
    '''
    # 位置更新方式，可选 ph_follow_ipc, ipc_follow_ph, separate
    STEP_TYPE = 'ph_follow_ipc'

    # 默认配置
    DEFAULT_CONFIG = {
        'pose': None,
        'scale': (1,1,1),

        'model_name': '',
        'model_data': {},
        'model_id': None,
        'model_z_val': False,

        'density': 1, # unit: kg/m^3
        'convex': False,
        'is_static': False,

        'type': 'normal'
        # 类型可选 normal（正常，受重力）, table（平板位置固定不变的桌子），fixed（固定不动的物体）
    }
    # 可视化 physx system 或 IPC system 中的物体，用于调试
    IPC_RENDER = False
    PHYSX_RENDER = True
    # 静态对象池，会自动管理所有的 TwinActor 对象
    ACTORS:list['TwinActor'] = []

    def __init__(self,
                 name: str,
                 scene: sapien.Scene,
                 ipc_entity: sapien.Entity,
                 physx_entity: sapien.Entity,
                 config: Dict[str, Any]):
        self.name = name
        self.scene = scene
        self.ipc_entity = ipc_entity
        self.physx_entity = physx_entity

        self.config = TwinActor.DEFAULT_CONFIG.copy()
        self.config.update(config)
        self.pose = config['pose'] \
            if config['pose'] is not None \
                else physx_entity.get_pose()
        self.type = self.config['type']

        if self.ipc_entity is not None:
            # IPC 系统中，component 只会按点移动，因此需要通过估计的方式得到位置
            self.init_ipc_pose = self.pose
            self.ipc_component: IPCABDComponent = self.ipc_entity.find_component_by_type(IPCABDComponent)
            self.init_pts:np.ndarray = self.ipc_component.get_positions().cpu().numpy()
        else:
            self.ipc_component = None
        TwinActor.ACTORS.append(self)
    
    @staticmethod
    def step_all(type:str):
        for actor in TwinActor.ACTORS:
            actor.step(type)

    @staticmethod
    def clear():
        for actor in TwinActor.ACTORS:
            actor.physx_entity.remove_from_scene()
            if actor.ipc_entity is not None:
                actor.ipc_entity.remove_from_scene()
        TwinActor.ACTORS.clear()
        
    @staticmethod
    def get_model_file(modelname, model_id, model_type='auto'):
        modeldir = os.path.join("./assets/objects", modelname)

        file_name , json_file_path = '', ''
        if model_type == 'auto' or model_type == 'obj':
            # try to load obj file
            if model_id is None:
                file_name = os.path.join(modeldir, "textured.obj")
                json_file_path = os.path.join(modeldir, "model_data.json")
            else:
                file_name = os.path.join(modeldir, f"textured{model_id}.obj")
                json_file_path = os.path.join(modeldir, f"model_data{model_id}.json")
            
            if os.path.exists(file_name):
                return file_name, json_file_path
        if model_type == 'auto' or model_type == 'glb':
            # try to load glb file
            if model_id is None:
                file_name = os.path.join(modeldir, "base.glb")
                json_file_path = os.path.join(modeldir, "model_data.json")
            else:
                file_name = os.path.join(modeldir, f"base{model_id}.glb")
                json_file_path = os.path.join(modeldir, f"model_data{model_id}.json")

            if os.path.exists(file_name):
                return file_name, json_file_path

        raise FileNotFoundError(f"{file_name}, {json_file_path} not found.")

    @classmethod
    def from_modelname(cls,
                       scene: sapien.Scene,
                       modelname: str,
                       pose: sapien.Pose,
                       scale = (1,1,1),
                       model_id = None,
                       model_z_val = False,
                       model_type: str = 'auto',
                       convex = False,
                       is_static = False,
                       disable_ipc = False,):
        '''
            create TwinActor from modelname and add entities to the scene
        '''
        file_name, json_file_path = TwinActor.get_model_file(
            modelname, model_id, model_type)
        try:
            with open(json_file_path, 'r') as file:
                model_data = json.load(file)
            scale = model_data["scale"]
        except:
            model_data = None

        if model_z_val:
            pose.set_p(pose.get_p()[:2].tolist() + [
                0.74 + (t3d.quaternions.quat2mat(pose.get_q()) @ (np.array(model_data["extents"]) * scale))[2]/2])

        # load physx entity
        builder = scene.create_actor_builder()
        if is_static:
            builder.set_physx_body_type("static")
        else:
            builder.set_physx_body_type("dynamic")

        if convex==True:
            builder.add_multiple_convex_collisions_from_file(
                filename=file_name,
                scale=scale,
                density=cls.DEFAULT_CONFIG['density']
            )
        else:
            builder.add_nonconvex_collision_from_file(
                filename=file_name,
                scale=scale,
            )

        builder.add_visual_from_file(
            filename=file_name,
            scale=scale
        )
        builder._mass = 0.01
        physx_entity = builder.build(name=f'{modelname}_physx')
        physx_entity.set_pose(pose)

        if not disable_ipc:
            # use shape of physx render component to set IPC component
            physx_render_component:RenderBodyComponent = physx_entity.find_component_by_type(
                RenderBodyComponent)
            render_shape:RenderShape = physx_render_component.render_shapes[0]
            vertices = render_shape.get_parts()[0].vertices
            triangles = render_shape.get_parts()[0].triangles
            if not cls.PHYSX_RENDER:
                physx_render_component.disable()
            collision_compoent:sapien.physx.PhysxRigidBaseComponent = physx_entity.find_component_by_type(sapien.physx.PhysxRigidBaseComponent)
            collision_shape: sapienp.PhysxCollisionShapeBox = collision_compoent.get_collision_shapes()[0]

            # load ipc entity
            ipc_entity = sapien.Entity()
            ipc_component = IPCABDComponent()
            tri_mesh = IPCTriMesh(scale=scale, vertices=vertices, triangles=triangles)
            ipc_component.set_tri_mesh(tri_mesh)
            ipc_component.set_density(TwinActor.DEFAULT_CONFIG['density'])
            ipc_component.set_friction(collision_shape.get_physical_material().dynamic_friction)
            

            if cls.IPC_RENDER:
                ipc_render_component = sapien.render.RenderCudaMeshComponent(
                    tri_mesh.n_vertices,
                    tri_mesh.n_surface_triangles
                )
                ipc_render_component.set_vertex_count(tri_mesh.n_vertices)
                ipc_render_component.set_triangle_count(tri_mesh.n_surface_triangles)
                ipc_render_component.set_triangles(tri_mesh.surface_triangles)
                ipc_render_component.set_material(
                    sapien.render.RenderMaterial(
                        base_color=[0.9, 0.3, 0.9, 0.6],
                    )
                )
                ipc_entity.add_component(ipc_render_component)
            ipc_entity.add_component(ipc_component)
            ipc_entity.set_pose(pose)
            ipc_entity.set_name(f'{modelname}_ipc')
            scene.add_entity(ipc_entity)
        else:
            ipc_entity = None

        return cls(
            name=modelname,
            scene=scene,
            ipc_entity=ipc_entity,
            physx_entity=physx_entity,
            config={
                'pose': pose,
                'scale': scale,
                'model_name': modelname,
                'model_data': model_data,
                'model_id': model_id,
                'model_z_val': model_z_val,
                'convex': convex,
                'is_static': is_static,
                'type': 'normal' if not is_static else 'fixed',
            },
        )

    @classmethod
    def from_physx(cls,
                   scene: sapien.Scene,
                   physx_entity:sapien.Entity,
                   scale = (1,1,1),
                   pose = None,
                   fixed = False,
                   disable_ipc = False):
        '''
            create TwinActor from physx entity and add entities to the scene
        '''
        pose = physx_entity.get_pose() if pose is None else pose
        name = physx_entity.get_name().replace('_physx', '')

        if not disable_ipc:
            # use shape of physx render component to set IPC component
            physx_render_component:RenderBodyComponent = physx_entity.find_component_by_type(
                RenderBodyComponent)
            render_shape:RenderShape = physx_render_component.render_shapes[0]
            vertices = render_shape.get_parts()[0].vertices
            triangles = render_shape.get_parts()[0].triangles
            if not cls.PHYSX_RENDER:
                physx_render_component.disable()
            collision_compoent:sapien.physx.PhysxRigidBaseComponent = physx_entity.find_component_by_type(sapien.physx.PhysxRigidBaseComponent)
            collision_shape: sapienp.PhysxCollisionShapeBox = collision_compoent.get_collision_shapes()[0]
            
            # load ipc entity
            ipc_entity = sapien.Entity()
            ipc_component = IPCABDComponent()
            tri_mesh = IPCTriMesh(scale=scale, vertices=vertices, triangles=triangles)
            ipc_component.set_tri_mesh(tri_mesh)
            ipc_component.set_density(TwinActor.DEFAULT_CONFIG['density'])
            ipc_component.set_friction(collision_shape.get_physical_material().dynamic_friction)

            if cls.IPC_RENDER:
                ipc_render_component = sapien.render.RenderCudaMeshComponent(
                    tri_mesh.n_vertices,
                    tri_mesh.n_surface_triangles
                )
                ipc_render_component.set_vertex_count(tri_mesh.n_vertices)
                ipc_render_component.set_triangle_count(tri_mesh.n_surface_triangles)
                ipc_render_component.set_triangles(tri_mesh.surface_triangles)
                ipc_render_component.set_material(
                    sapien.render.RenderMaterial(
                        base_color=[0.9, 0.3, 0.9, 0.6],
                    )
                )
                ipc_entity.add_component(ipc_render_component)
            ipc_entity.add_component(ipc_component)
            ipc_entity.set_pose(pose)
            ipc_entity.set_name(f'{name}_ipc')
            scene.add_entity(ipc_entity)
        else:
            ipc_entity = None
        
        return cls(
            name=name,
            scene=scene,
            ipc_entity=ipc_entity,
            physx_entity=physx_entity,
            config={
                'pose': pose,
                'scale': scale,
                'type': 'normal' if not fixed else 'fixed',
            },
        )
    
    @staticmethod
    def get_default_material(scene: sapien.Scene) -> sapien.physx.PhysxMaterial:
        if hasattr(scene, 'default_physical_material'):
            return scene.default_physical_material
        else:
            return scene.create_physical_material(
                0.5, 0.5, 0)
    
    @classmethod
    def as_table(cls,
                 scene: sapien.Scene,
                 pose: sapien.Pose,
                 length: float,
                 width: float,
                 height: float,
                 thickness=0.1,
                 color=(1, 1, 1),
                 name="table",
                 is_static = True,
                 texture_id = None,
                 disable_ipc = False
                 ):
        '''
            create TwinActor as table. Only the tabletop is in IPC system.
            And add entities to the scene
        '''

        builder = scene.create_actor_builder()
        if is_static:
            builder.set_physx_body_type("static")
        else:
            builder.set_physx_body_type("dynamic")

        # Tabletop
        tabletop_pose = sapien.Pose([0.0, 0.0, -thickness / 2])  # Center the tabletop at z=0
        tabletop_half_size = [length / 2, width / 2, thickness / 2]
        builder.add_box_collision(
            pose=tabletop_pose,
            half_size=tabletop_half_size,
            material=cls.get_default_material(scene)
        )

        # Add texture
        if texture_id is not None:
            # test for both .png and .jpg
            texturepath = f"./assets/textures/{texture_id}.png"
            # create texture from file
            texture2d = sapien.render.RenderTexture2D(texturepath)
            material = sapien.render.RenderMaterial()
            material.set_base_color_texture(texture2d)
            # renderer.create_texture_from_file(texturepath)
            # material.set_diffuse_texture(texturepath)
            material.base_color = [1, 1, 1, 1]
            material.metallic = 0.1
            material.roughness = 0.3
            builder.add_box_visual(
                pose=tabletop_pose, half_size=tabletop_half_size, material=material
            )
        else:
            builder.add_box_visual(
                pose=tabletop_pose, half_size=tabletop_half_size, material=color,
            )

        # Table legs (x4)
        leg_spacing = 0.1
        for i in [-1, 1]:
            for j in [-1, 1]:
                x = i * (length / 2 - leg_spacing / 2) 
                y = j * (width / 2 - leg_spacing / 2)
                table_leg_pose = sapien.Pose([x, y, -height / 2])
                table_leg_half_size = [thickness / 2, thickness / 2, height / 2]
                builder.add_box_collision(
                    pose=table_leg_pose, half_size=table_leg_half_size
                )
                builder.add_box_visual(
                    pose=table_leg_pose, half_size=table_leg_half_size, material=color
                )
        builder.set_initial_pose(pose)
        physx_table = builder.build(name=name)

        if not disable_ipc:
            # create ipc tabletop
            # use shape of physx render component to set IPC component
            physx_render_component:RenderBodyComponent = physx_table.find_component_by_type(
                RenderBodyComponent)
            render_shape:RenderShape = physx_render_component.render_shapes[0]
            vertices = render_shape.get_parts()[0].vertices
            triangles = render_shape.get_parts()[0].triangles
            if not cls.PHYSX_RENDER:
                physx_render_component.disable()
            collision_compoent:sapien.physx.PhysxRigidBaseComponent = physx_table.find_component_by_type(sapien.physx.PhysxRigidBaseComponent)
            collision_shape: sapienp.PhysxCollisionShapeBox = collision_compoent.get_collision_shapes()[0]

            # load ipc entity
            ipc_entity = sapien.Entity()
            ipc_component = IPCABDComponent()
            tri_mesh = IPCTriMesh(
                scale=[length/2, width/2, thickness/2],
                vertices=vertices, triangles=triangles
            )
            ipc_component.set_tri_mesh(tri_mesh)
            ipc_component.set_density(TwinActor.DEFAULT_CONFIG['density'])
            ipc_component.set_friction(collision_shape.get_physical_material().dynamic_friction)

            if cls.IPC_RENDER:
                ipc_render_component = sapien.render.RenderCudaMeshComponent(
                    tri_mesh.n_vertices,
                    tri_mesh.n_surface_triangles
                )
                ipc_render_component.set_vertex_count(tri_mesh.n_vertices)
                ipc_render_component.set_triangle_count(tri_mesh.n_surface_triangles)
                ipc_render_component.set_triangles(tri_mesh.surface_triangles)
                ipc_render_component.set_material(
                    sapien.render.RenderMaterial(
                        base_color=[0.9, 0.3, 0.9, 0.6],
                    )
                )
                ipc_entity.add_component(ipc_render_component)
            ipc_entity.add_component(ipc_component)
            ipc_entity.set_name(f'{name}_ipc')
            ipc_entity.set_pose(sapien.Pose(
                pose.p-[0, 0, thickness/2], pose.q
            ))
            scene.add_entity(ipc_entity)
        else:
            ipc_entity = None

        return cls(
            name=name,
            scene=scene,
            ipc_entity=ipc_entity,
            physx_entity=physx_table,
            config={
                'pose': pose,
                'scale': [length/2, width/2, thickness/2],
                'type': 'table',
            },
        )

    def get_ipc_pose(self) -> sapien.Pose:
        '''通过 IPC 系统中的顶点位置估计实体位置'''
        if self.ipc_component is None:
            return self.pose
    
        now_pts = self.ipc_component.get_positions().cpu().numpy()
        R, t = estimate_rigid_transform(self.init_pts, now_pts)
        q_R = t3d.quaternions.mat2quat(np.linalg.inv(R))
        # 平移
        p = t + self.init_ipc_pose.p @ R
        # 旋转
        q = quat_product(q_R, self.init_ipc_pose.q)
        return sapien.Pose(p, q)

    def step(self, type:str):
        '''更新实体位置，type 为更新时机，可取 b(before ipc step), a(after ipc step)'''
        if self.type == 'table':
            if self.ipc_component and type == 'b':
                self.ipc_component.set_kinematic_target_pose(sapien.Pose(
                    self.pose.p - [0, 0, self.config['scale'][2]],
                    self.pose.q
                ))
            # self.physx_entity.set_pose(self.pose)
        elif self.type == 'fixed':
            if self.ipc_component and type == 'b':
                self.ipc_component.set_kinematic_target_pose(self.pose)
        else:
            if self.STEP_TYPE == 'ph_follow_ipc':
                if type == 'a':
                    self.pose = self.get_ipc_pose()
                    self.physx_entity.set_pose(self.pose)
            elif self.STEP_TYPE == 'ipc_follow_ph':
                if type == 'b':
                    self.pose = self.physx_entity.get_pose()
                    if self.ipc_component:
                        self.ipc_component.set_kinematic_target_pose(self.pose)

    def set_pose(self, pose:sapien.Pose):
        '''
            set pose both in PhysX system and IPC system
            You need to run scene.step and ipc_system.step to apply this operation!
        '''
        self.pose = pose
        self.physx_entity.set_pose(pose)
        if self.ipc_component:
            if self.type == 'table':
                self.ipc_component.set_kinematic_target_pose(sapien.Pose(
                    self.pose.p - [0, 0, self.config['scale'][2]],
                    self.pose.q
                ))
            else:
                self.ipc_component.set_kinematic_target_pose(self.pose)
    
    def get_pose(self) -> sapien.Pose:
        return self.pose

# create box
def ipc_create_box(
    scene: sapien.Scene,
    pose: sapien.Pose,
    half_size,
    color=None,
    name="",
    texture_id=None,
    fixed=False,
    disable_ipc=False
) -> TwinActor:
    entity = sapien.Entity()
    entity.set_name(name)

    # create PhysX dynamic rigid body
    rigid_component = sapien.physx.PhysxRigidDynamicComponent() if not fixed else sapien.physx.PhysxRigidStaticComponent()
    rigid_component.attach(
        sapien.physx.PhysxCollisionShapeBox(
            half_size=half_size,
            material=TwinActor.get_default_material(scene)
        )
    )

    # Add texture
    if texture_id is not None:
        # test for both .png and .jpg
        texturepath = f"./assets/textures/{texture_id}.png"
        # create texture from file
        texture2d = sapien.render.RenderTexture2D(texturepath)
        material = sapien.render.RenderMaterial()
        material.set_base_color_texture(texture2d)
        # renderer.create_texture_from_file(texturepath)
        # material.set_diffuse_texture(texturepath)
        material.base_color = [1, 1, 1, 1]
        material.metallic = 0.1
        material.roughness = 0.3
    else:
        if len(color) == 3:
            material = sapien.render.RenderMaterial(base_color=[*color[:3], 1])
        else:
            material = sapien.render.RenderMaterial(base_color=np.array(color).tolist())

    # create render body for visualization
    render_component = sapien.render.RenderBodyComponent()
    render_component.attach(
        # add a box visual shape with given size and rendering material
        sapien.render.RenderShapeBox(
            half_size, material
        )
    )

    entity.add_component(rigid_component)
    entity.add_component(render_component)
    entity.set_pose(pose)
    scene.add_entity(entity)

    return TwinActor.from_physx(
        scene=scene,
        physx_entity=entity,
        scale=half_size,
        fixed=fixed,
        disable_ipc=disable_ipc
    )


# create cylinder
def ipc_create_cylinder(
    scene: sapien.Scene,
    pose: sapien.Pose,
    radius: float,
    half_length: float,
    color=None,
    name="",
    disable_ipc=False
) -> TwinActor:
    entity = sapien.Entity()
    entity.set_name(name)
    entity.set_pose(pose)

    # create PhysX dynamic rigid body
    rigid_component = sapien.physx.PhysxRigidDynamicComponent()
    rigid_component.attach(
        sapien.physx.PhysxCollisionShapeCylinder(
            radius=radius, half_length=half_length,
            material=TwinActor.get_default_material(scene)
        )
    )

    # create render body for visualization
    render_component = sapien.render.RenderBodyComponent()
    render_component.attach(
        # add a box visual shape with given size and rendering material
        sapien.render.RenderShapeCylinder(
            radius=radius, half_length=half_length,
            material=sapien.render.RenderMaterial(base_color=[*color[:3], 1])
        )
    )

    entity.add_component(rigid_component)
    entity.add_component(render_component)
    entity.set_pose(pose)

    return TwinActor.from_physx(
        scene=scene,
        physx_entity=entity,
        scale=[radius, radius, half_length],
        disable_ipc=disable_ipc
    )

def ipc_create_table(
    scene: sapien.Scene,
    pose: sapien.Pose,
    length: float,
    width: float,
    height: float,
    thickness=0.1,
    color=(1, 1, 1), 
    name="table",
    is_static = True,
    texture_id = None,
    disable_ipc = False
) -> TwinActor:
    """Create a table with specified dimensions."""
    return TwinActor.as_table(
        scene=scene,
        pose=pose,
        length=length,
        width=width,
        height=height,
        thickness=thickness,
        color=color,
        name=name,
        is_static=is_static,
        texture_id=texture_id,
        disable_ipc=disable_ipc
    )

# create obj model
def ipc_create_obj(
    scene: sapien.Scene,
    pose: sapien.Pose,
    modelname: str,
    scale = (1,1,1),
    convex = False,
    is_static = False,
    model_id = None,
    model_z_val = False,
    disable_ipc = False
) -> TwinActor:
    twin = TwinActor.from_modelname(
        scene=scene,
        pose=pose,
        modelname=modelname,
        scale=scale,
        convex=convex,
        is_static=is_static,
        model_id=model_id,
        model_z_val=model_z_val,
        model_type='obj',
        disable_ipc=disable_ipc
    )
    return twin, twin.config.get('model_data', {})


# create glb model
def ipc_create_glb(
    scene: sapien.Scene,
    pose: sapien.Pose,
    modelname: str,
    scale = (1,1,1),
    convex = False,
    is_static = False,
    model_id = None,
    model_z_val = False,
    disable_ipc = False
) -> TwinActor:
    twin = TwinActor.from_modelname(
        scene=scene,
        pose=pose,
        modelname=modelname,
        scale=scale,
        convex=convex,
        is_static=is_static,
        model_id=model_id,
        model_z_val=model_z_val,
        model_type='glb',
        disable_ipc=disable_ipc
    )
    return twin, twin.config.get('model_data', {})


def ipc_create_actor(
    scene: sapien.Scene,
    pose: sapien.Pose,
    modelname: str,
    scale = (1,1,1),
    convex = False,
    is_static = False,
    model_id = None,
    model_z_val = False,
    disable_ipc = False,
) -> tuple[TwinActor, dict]:
    twin = TwinActor.from_modelname(
        scene=scene,
        pose=pose,
        modelname=modelname,
        scale=scale,
        convex=convex,
        is_static=is_static,
        model_id=model_id,
        model_z_val=model_z_val,
        disable_ipc=disable_ipc
    )
    return twin, twin.config.get('model_data', {})