
import sys
sys.path.append('./') 
sys.path.append(f'./policy')
from envs import CONFIGS_PATH

import os
import numpy as np
from pathlib import Path
from collections import deque
import traceback

import yaml
from datetime import datetime
import importlib
from argparse import ArgumentParser
import pdb

current_file_path = os.path.abspath(__file__)
parent_directory = os.path.dirname(current_file_path)

def class_decorator(task_name):
    envs_module = importlib.import_module(f'envs.{task_name}')
    try:
        env_class = getattr(envs_module, task_name)
        env_instance = env_class()
    except:
        raise SystemExit("No Task")
    return env_instance

def eval_function_decorator(policy_name, model_name):
    try:
        policy_model = importlib.import_module(policy_name)
        func = getattr(policy_model, model_name)
        return func
    except ImportError as e:
        raise e

def get_camera_config(camera_type):
    camera_config_path = os.path.join(parent_directory, '../task_config/_camera_config.yml')

    assert os.path.isfile(camera_config_path), "task config file is missing"

    with open(camera_config_path, 'r', encoding='utf-8') as f:
        args = yaml.load(f.read(), Loader=yaml.FullLoader)

    assert camera_type in args, f'camera {camera_type} is not defined'
    return args[camera_type]

def main(usr_args):
    task_name = usr_args.task_name
    head_camera_type = usr_args.head_camera_type
    # checkpoint_num = usr_args.checkpoint_num
    policy_name = usr_args.policy_name
    ckpt_folder_path = os.path.join(f'./policy/{policy_name}/checkpoints/', usr_args.ckpt_folder)
    save_dir = Path(f'eval_result/{policy_name}/{task_name}')
    video_save_dir = None
    video_size = None

    save_dir.mkdir(parents=True, exist_ok=True)

    get_model = eval_function_decorator(policy_name, 'get_model')

    with open(f'./task_config/{task_name}.yml', 'r', encoding='utf-8') as f:
        args = yaml.load(f.read(), Loader=yaml.FullLoader)

    if args['eval_video_log']:
        video_save_dir = Path(f'eval_video/{policy_name}/{task_name}/{usr_args.ckpt_folder}') 
        camera_config = get_camera_config(str(args['head_camera_type']))
        video_size = str(camera_config['w']) + 'x' + str(camera_config['h'])
        video_save_dir.mkdir(parents=True, exist_ok=True)
        args['eval_video_save_dir'] = video_save_dir

    embodiment_type = args.get('embodiment')
    embodiment_config_path = os.path.join(CONFIGS_PATH, '_embodiment_config.yml')

    with open(embodiment_config_path, 'r', encoding='utf-8') as f:
        _embodiment_types = yaml.load(f.read(), Loader=yaml.FullLoader)

    with open(CONFIGS_PATH + '_camera_config.yml', 'r', encoding='utf-8') as f:
        _camera_config = yaml.load(f.read(), Loader=yaml.FullLoader)
    
    args['head_camera_h'] = _camera_config[head_camera_type]['h']
    args['head_camera_w'] = _camera_config[head_camera_type]['w']
    
    def get_embodiment_file(embodiment_type):
        robot_file = _embodiment_types[embodiment_type]['file_path']
        if robot_file is None:
            raise "No embodiment files"
        return robot_file
    
    def get_embodiment_config(robot_file):
        robot_config_file = os.path.join(robot_file, 'config.yml')
        with open(robot_config_file, 'r', encoding='utf-8') as f:
            embodiment_args = yaml.load(f.read(), Loader=yaml.FullLoader)
        return embodiment_args
    
    if len(embodiment_type) == 1:
        args['left_robot_file'] = get_embodiment_file(embodiment_type[0])
        args['right_robot_file'] = get_embodiment_file(embodiment_type[0])
        args['dual_arm_embodied'] = True
    elif len(embodiment_type) == 3:
        args['left_robot_file'] = get_embodiment_file(embodiment_type[0])
        args['right_robot_file'] = get_embodiment_file(embodiment_type[1])
        args['embodiment_dis'] = embodiment_type[2]
        args['dual_arm_embodied'] = False
    else:
        raise "embodiment items should be 1 or 3"
    
    args['left_embodiment_config'] = get_embodiment_config(args['left_robot_file'])
    args['right_embodiment_config'] = get_embodiment_config(args['right_robot_file'])
    
    if len(embodiment_type) == 1:
        embodiment_name = str(embodiment_type[0])
    else:
        embodiment_name = str(embodiment_type[0]) + '_' + str(embodiment_type[1])

    # output camera config
    print('============= Config =============\n')
    print('Head Camera Config: '+ str(args['head_camera_type']) + f', ' + str(args['collect_head_camera']))
    print('Wrist Camera Config: '+ str(args['wrist_camera_type']) + f', ' + str(args['collect_wrist_camera']))
    print('Embodiment Config:: '+ embodiment_name)
    print('\n=======================================')

    task = class_decorator(args['task_name'])
    args['policy_name'] = policy_name
    
    seed = 0
    st_seed = 100000 * (1+seed)
    suc_nums = []
    test_num = 100
    if task_name == 'classify_tactile':
        test_num = 20
    topk = 1
    model = get_model(ckpt_folder_path, task_name)
    st_seed, suc_num, task_reward = test_policy(task_name, task, args, model, st_seed, test_num=test_num, video_size = video_size)
    suc_nums.append(suc_num)

    topk_success_rate = sorted(suc_nums, reverse=True)[:topk]

    file_path = save_dir / f'{str(usr_args.ckpt_folder)}.txt'
    os.makedirs(Path(file_path).parent, exist_ok=True)
    
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    with open(file_path, 'w') as file:
        file.write(f'Timestamp: {current_time}\n\n')
        file.write(str(task_reward) + '\n')
        file.write('\n'.join(map(str, np.array(suc_nums) / test_num)))

    print(f'Data has been saved to {file_path}')
    return task_reward
    

def test_policy(task_name, TASK_ENV, args, model, st_seed, test_num=20, video_size=None):
    expert_check = True
    print("Task name: ", args["task_name"])
    print('Policy name: ', args["policy_name"])

    TASK_ENV.suc = 0
    TASK_ENV.test_num = 0

    now_id = 0
    succ_seed = 0
    suc_test_seed_list = []

    policy_name = args['policy_name']
    eval_func = eval_function_decorator(policy_name, 'eval')
    reset_func = eval_function_decorator(policy_name, 'reset_model')
    
    now_seed = st_seed
    task_total_reward = 0
    while succ_seed < test_num:
        render_freq = args['render_freq']
        args['render_freq'] = 0
        
        if expert_check:
            try:
                TASK_ENV.setup_demo(now_ep_num=now_id, seed = now_seed, is_test = True, ** args)
                TASK_ENV.play_once()
                TASK_ENV.close()
            except Exception as e:
                stack_trace = traceback.format_exc()
                print(' -------------')
                print('Error: ', stack_trace)
                print(' -------------')
                TASK_ENV.close()
                now_seed += 1
                args['render_freq'] = render_freq
                print('error occurs !')
                continue

        if (not expert_check) or ( TASK_ENV.plan_success and TASK_ENV.check_success() ):
            succ_seed +=1
            suc_test_seed_list.append(now_seed)
        else:
            now_seed += 1
            args['render_freq'] = render_freq
            continue

        args['render_freq'] = render_freq
        TASK_ENV.setup_demo(now_ep_num=now_id, seed = now_seed, is_test = True, ** args)
        TASK_ENV.test_num += 1

        if TASK_ENV.eval_video_path is not None:
            import subprocess
            ffmpeg = subprocess.Popen([
                'ffmpeg', '-y',
                '-f', 'rawvideo',
                '-pixel_format', 'rgb24',
                '-video_size', video_size,
                '-framerate', '10',
                '-i', '-',
                '-loglevel', 'quiet',
                '-pix_fmt', 'yuv420p',
                '-vcodec', 'libx264',
                '-crf', '23',
                f'{TASK_ENV.eval_video_path}/{TASK_ENV.test_num}.mp4'
            ], stdin=subprocess.PIPE)
            TASK_ENV._set_eval_video_ffmpeg(ffmpeg)

        succ = False
        reset_func(model)
        while TASK_ENV.take_action_cnt < TASK_ENV.step_lim:
            observation = TASK_ENV.get_obs()
            eval_func(TASK_ENV, model, observation)
            if TASK_ENV.eval_success_cvpr:
                succ = True
                break
        print('reward:', TASK_ENV.cvpr_score) 
        task_total_reward += TASK_ENV.cvpr_score
        TASK_ENV._del_eval_video_ffmpeg()

        if succ:
            TASK_ENV.suc += 1
            print("\nsuccess!")
        else:
            print("\nfail!")

        now_id += 1
        TASK_ENV.close()
        if TASK_ENV.render_freq:
            TASK_ENV.viewer.close()

        print(f"{task_name} success rate: {TASK_ENV.suc}/{TASK_ENV.test_num}, current seed: {now_seed}\n")
        # TASK_ENV._take_picture()
        now_seed += 1

    return now_seed, TASK_ENV.suc, task_total_reward

if __name__ == "__main__":
    from test_render import Sapien_TEST
    Sapien_TEST()
    
    parser = ArgumentParser()

    # Add arguments
    # TODO: modify the corresponding argument according to your policy.
    parser.add_argument('task_name', type=str, default='block_hammer_beat')
    parser.add_argument('head_camera_type', type=str)
    parser.add_argument('policy_name', type=str)
    parser.add_argument('ckpt_folder', type=str)
    # parser.add_argument('expert_data_num', type=int, default=20)
    # parser.add_argument('checkpoint_num', type=int, default=1000)
    # parser.add_argument('seed', type=int, default=0)
    usr_args = parser.parse_args()
    
    reward = main(usr_args)
    print(reward)

