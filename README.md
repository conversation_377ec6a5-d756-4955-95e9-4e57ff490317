<h1 align="center">
	RoboTwin Dual-Arm Collaboration Challenge<br>2nd MEIS Workshop @ CVPR2025<br>
</h1>

![](./files/poster.png)

<h2><a href="https://robotwin-benchmark.github.io/cvpr-2025-challenge/">Webpage</a> | <a href="https://developer.d-robotics.cc/en/cvpr-2025-challenge">D-Robotics Cloud Platform</a> | <a href="https://docs.google.com/forms/d/e/1FAIpQLSdibD7TEROlNOO8IFgGmezjUCjELqKHvaUtvj2Nv281hHPZTg/viewform?usp=header">Registration</a></h2> 

Organizers: <a href="https://yaomarkmu.github.io/"><PERSON></a>, <a href="https://tianxingchen.github.io">T<PERSON><PERSON></a>, <a href="http://luoping.me/"><PERSON></a>, <a href="https://english.seiee.sjtu.edu.cn/english/detail/842_802.htm"><PERSON><PERSON><PERSON></a>, <a href="https://www.eecs.utk.edu/people/fei-liu/">Fei Liu</a>, <a href="https://web.stanford.edu/~schwager/">Mac Schwager</a>, <a href="https://www.intelligentrobotics-acrossscales.com/">Dandan Zhang</a>, Zhiqiang Xie, Yusen Qin, <a href="https://dingmyu.github.io/">Mingyu Ding</a>, Zanxin Chen, Kaixuan Wang, Baijun Chen.

**RoboTwin**, accepted to <i style="color: red; display: inline;"><b>CVPR 2025</b></i> and <i style="color: red; display: inline;"><b>ECCV Workshop 2024 (Best Paper Award)</b></i>:<br>[Webpage](https://robotwin-benchmark.github.io/early-version) | [PDF](https://arxiv.org/pdf/2409.02920) | [arXiv](https://arxiv.org/abs/2409.02920)<br>
<img src="https://hits.seeyoufarm.com/api/count/incr/badge.svg?url=https%3A%2F%2Fgithub.com%2FTianxingChen%2FRoboTwin&count_bg=%23184FFF&title_bg=%23E116E5&icon=&icon_color=%23E7E7E7&title=Repo+Viewers&edge_flat=true"/><img alt="GitHub Repo stars" src="https://img.shields.io/github/stars/TianxingChen/RoboTwin">

**Hardware Support**: AgileX Robotics (松灵机器人)
**Software Support**: D-robotics (地平线地瓜机器人)

**🏆 Awards (Bonus only for Real-world Track)**: 
🥇 First Prize ($1500)
🥈 Second Prize ($1000)
🥉 Third Prize ($500)


# 👌 Important Updates
* **2025/3/31**, We updated [Guide to debugging evaluation](./evaluation_debugging_image_guide.md) !
* **2025.03.28**, We have released tactile code, please follow the [Install.md](./INSTALL.md). **Please run `script/_download_assets.sh` again**, we have updated the assets for tactile manipulation, which does not affect the rigid object manipulation part.
* **2025.03.20**, We've done the online briefing and released the code. The tactile task code is still pending release.

# 🛠️ Installation
> Please note that you need to strictly follow the steps: **Modify `mplib` Library Code** and **Download Assert**.

Clone this Repo:
```
git clone -b CVPR-Challenge-2025 https://github.com/TianxingChen/RoboTwin.git
```

See [INSTALL.md](./INSTALL.md) for installation instructions. The installation process takes about 20 minutes.

# 🧑🏻‍💻 Usage 
## 1. Task Running and Data Collection
Running the following command will first search for a random seed for the target collection quantity (default is 100), and then replay the seed to collect data.

```
bash run_task.sh ${task_name} ${gpu_id}
# As example: bash run_task.sh empty_cup_place 0
```

<table>
  <tr>
    <td><img src="files/imgs/Video1.gif" alt="Video1"></td>
    <td><img src="files/imgs/Video2.gif" alt="Video2"></td>
    <td><img src="files/imgs/Video3.gif" alt="Video3"></td>
  </tr>
  <tr>
    <td><img src="files/imgs/Video4.gif" alt="Video4"></td>
    <td><img src="files/imgs/Video5.gif" alt="Video5"></td>
    <td><img src="files/imgs/Video6.gif" alt="Video6"></td>
  </tr>
</table>



## 2. Task Config
> We strongly recommend you to view [Config Tutorial](./CONFIG_TUTORIAL.md) for more details.

Data collection configurations are located in the `config` folder, corresponding to each task. 

For the official evaluation, all cameras are designated as D435, and the point cloud is downsampled to 1024 by default. You can see its configuration in `task_config/_camera_config.yml`.

## 3. Deploy your policy

> For competition fairness, the evaluation - platform test seeds are invisible. For the `put_bottles_dustbin` task, bottles may not stand well on the table in some seeds. We've carefully screened the test seeds to avoid this.

Please build your project in the root directory `policy/Your-Policy`. You may change the project folder name if needed.

```
# default file structure
Your-Policy
├── checkpoints
│   └── __init__.py
│   └── ckpt1
│       └── ...
│   └── ckpt2
│       └── ...
├── data
│   └── __init__.py
├── deploy_policy.py
├── requirement.txt
└── pyproject.toml
```

For the project environment setup, please use `requirement.txt` as much as possible. We'll run `pip install -r requirement.txt` by default. If you have special requirements, feel free to contact us via email (`<EMAIL>`).

For policy deployment, simply modify the contents of policy/Your-Policy/deploy_policy.py in the root directory according to the instructions. If you encounter any issues or have questions, please submit them as issues.

**Please ensure that the names and parameters of our predefined default functions (`encode_obs`, `get_model`, `eval`, `reset_model`) are not modified.**

```
# import packages and module here

def encode_obs(observation): # Post-Process Observation
    obs = observation
    # ...
    return obs

def get_model(ckpt_folder, task_name): # keep 
    print('ckpt_folder: ', ckpt_folder)
    return Your_Policy(ckpt_folder, task_name) # load your model

def eval(TASK_ENV, model, observation):
    '''
        All the function interfaces below are just examples 
        You can modify them according to your implementation
        But we strongly recommend keeping the code logic unchanged
    '''
    obs = encode_obs(observation) # Post-Process Observation

    if len(model.obs_cache) == 0: # Force an update of the observation at the first frame to avoid an empty observation window
        model.update_obs(obs)    

    actions = model.get_action() # Get Action according to observation chunk

    for action in actions: # Execute each step of the action
        TASK_ENV.take_action(action)
        observation = TASK_ENV.get_obs()
        obs = encode_obs(observation)
        model.update_obs(obs) # Update Observation

def reset_model(model): # Clean the model cache at the beginning of every evaluation episode, such as the observation window
    pass
```

Finally, run the script in the root directory using the command `bash eval_policy.sh ${policy_name} ${task_name} ${ckpt_folder_path} ${gpu_id}` to evaluate your model's performance on specific tasks.

Here is what each parameter means:
- `${policy_name}`: Name of your policy's folder within the `policy` directory, default as `Your-Policy`.
- `${task_name}`: Name of the task for evaluation.
- `${ckpt_folder}`: Relative path of your checkpoint folder concerning the `policy/${policy_name}/checkpoints` folder. Our code will convert this relative path to an absolute one and pass it to the `get_model` function for model loading.

Please note that you should not modify this script. The parameters provided are sufficient for model loading and evaluation.

```
# eval_policy.sh
export VISION_TACTILE_ON=0

DEBUG=False

policy_name=${1}
task_name=${2}
ckpt_folder=${3}
gpu_id=${4}
head_camera_type="D435"

export HYDRA_FULL_ERROR=1
export CUDA_VISIBLE_DEVICES=${gpu_id}

if [ ${task_name} == "classify_tactile" ]; then
    export VISION_TACTILE_ON=1
fi

python ./script/eval_policy.py "$task_name" "$head_camera_type" "$policy_name" "$ckpt_folder"   
```

# 🚴‍♂️ Baselines

## 1. Diffusion Policy
The DP code can be found in `policy/Diffusion-Policy`.

Install Diffusion-Policy
```
pip install -e policy/Diffusion-Policy
```

Process Data for DP training after collecting data (In root directory)
```
python script/pkl2zarr_dp.py ${task_name} ${head_camera_type} ${expert_data_num}
# As example: python script/pkl2zarr_dp.py empty_cup_place D435 100, which indicates preprocessing of 100 empty_cup_place task trajectory data using D435 camera.
```

Then, move to `policy/Diffusion-Policy` first, and run the following code to train DP. The model will be trained for 300 epochs:
```
bash train.sh ${task_name} ${expert_data_num} ${seed} ${gpu_id}
# As example: bash train.sh empty_cup_place 100 0 0
```

Run the following code in the Project Root to evaluate DP for a specific task for 100 times:
```
bash eval_policy.sh
# As example: bash eval_policy.sh Diffusion-Policy empty_cup_place test/300.ckpt 0 0
```

## 2. 3D-Diffusion Policy
The 3D-Diffusion-Policy (DP3) code can be found in `policy/3D-Diffusion-Policy`.

Install 3D-Diffusion-Policy
```
pip install -r policy/3D-Diffusion-Policy/requirement.txt
```

Process Data for DP3 training after collecting data (In root directory)
```
python script/pkl2zarr_dp3.py ${task_name} ${expert_data_num}
# As example: python script/pkl2zarr_dp3.py empty_cup_place 100, which indicates preprocessing of 100 `empty_cup_place` task trajectory data.
```

Then, move to `policy/3D-Diffusion-Policy` first, and run the following code to train DP3. The model will be trained for 3000 epochs:
```
bash train.sh ${task_name} ${expert_data_num} ${seed} ${gpu_id}
# As example: bash train.sh empty_cup_place 100 0 0
```

Run the following code in the **Project Root** to evaluate DP3 for a specific task for 100 times:
```
bash eval_policy.sh ${policy_name} ${task_name} ${ckpt_folder} ${gpu_id}
# As example: bash eval_policy.sh 3D-Diffusion-Policy empty_cup_place empty_cup_place_D435_50_0 0
```

# 🏋️ Submit Your Solution

**See [SUBMIT_YOUR_SOLUTION.md](./SUBMIT_YOUR_SOLUTION.md) for more details.**

We offer a per - task evaluation environment with a **single 4090 GPU (24G)**.

## D-Robotics Cloud Platform
> YOUR_API_KEY, username and password will be provided through email upon registration.

Website: [https://developer.d-robotics.cc/en/cvpr-2025-challenge](https://developer.d-robotics.cc/en/cvpr-2025-challenge)

## How to Submit

### Provide Necessary File to Setup the Evaluation Environment

Our evaluation environment is configured with the libraries specified in [Install.md](./INSTALL.md) by default, with the environment set to cuda 12.1, python 3.10, and `torch==2.4.1`. Please do not include `mplib` in `requirements.txt`. It will overwrite our modified mplib library file.

Please fill in the `requirements.txt` or `pyproject.toml` file, and we will automatically run it to configure your environment.

### Submit Code and ckpt
Specifically, in our command, `/path/to/your/policy` specifies the code folder, and `/path/to/your/checkpoint` specifies the checkpoint folder under `policy/Your-Policy/checkpoints`. By default, we will filter out the data in `/path/to/your/policy` and any non-specified checkpoints in the checkpoints folder to avoid uploading large, unnecessary files.

```bash
submit upload --api-key YOUR_API_KEY --submission-id YOUR_SUBMISSION_ID --dir /path/to/your/policy --checkpoint-dir /path/to/your/checkpoint
# /path/to/your/policy: path to `Project_Root/policy` + the folder name of your policy under `Project_Root/policy`, default is `Your-Policy`
# /path/to/your/checkpoint: path to `Project_Root/policy/Your-Policy/chekpoints` + the folder name of your checkpoints under `Project_Root/policy/Your-Policy/checkpoints`, such as `ckpt1`
#
# default file structure: 
# Your-Policy
# ├── checkpoints
# │   └── __init__.py
# │   └── ckpt1
# │       └── ...
# │   └── ckpt2
# │       └── ...
# ├── data
# │   └── __init__.py
# ├── deploy_policy.py
# └── requirement.txt or pyproject.toml
```

**This design isolates your code from the environment code, ensuring competition fairness. We believe it suffices for you to run and deploy the evaluation.**

# 🚀 Task Information
### empty_cup_place

At initialization, the cup and the coaster will be randomly placed on the desk. The goal is to place the cup on the coaster. This task is worth 10 points. The coaster can be moved.

### dual_shoes_place

At initialization, the shoes will be randomly placed, and the shoe box will be fixed at a certained location. The goal is to place the shoes in the shoe box, with orientation to the left. For first shoe placed successfully you will earn 6 points, and for the second you will earn 14 points more.

### put_bottles_dustbin

At initialization, the three bottles will be randomly placed on the desk. The goal is to put the bottles into the trash.
- 5 points for successfully placing one bottle
- 12.5 points for two
- 25 points for three

### blocks_stack_hard

At initialization, the three cubes will be randomly placed on the desk. The goal is to stack the cubes in the order of: red, green, blue from bottom up. You will earn 10 points for stacking the green cube on the red one, and 15 more for stacking the blue cube on the green one. All the three cubes can be moved.

### bowls_stack

At initialization, the three bowls will be randomly placed on the desk. The goal is to stack the bowls. You will earn 6 points for stacking two bowls, and 14 more for the third.

### classify_tactile (Tactile)

At initialization, a object will be place on the desk, along with two mat(red and green). The object will be one of the following two: a rectangular cuboid or a 25-sided prism. The object may be on the left hand side or right hand side. You are encouraged to identify which type the object is. If it is a rectangular cuboid, place it on the red mat; otherwise, place it on the green mat. This is worth 5 points as bonus.

# 🔗 Official WeChat Group QR Code

<img src="./files/wechat-group.jpg" alt="wechat-group" style="width:30%;">

# 👍 Citation
If you find our work useful, please consider citing:

RoboTwin: Dual-Arm Robot Benchmark with Generative Digital Twins, accepted to <i style="color: red; display: inline;"><b>CVPR 2025 (Highlight)</b></i>
```
@misc{mu2025robotwindualarmrobotbenchmark,
      title={RoboTwin: Dual-Arm Robot Benchmark with Generative Digital Twins}, 
      author={Yao Mu and Tianxing Chen and Zanxin Chen and Shijia Peng and Zhiqian Lan and Zeyu Gao and Zhixuan Liang and Qiaojun Yu and Yude Zou and Mingkun Xu and Lunkai Lin and Zhiqiang Xie and Mingyu Ding and Ping Luo},
      year={2025},
      eprint={2504.13059},
      archivePrefix={arXiv},
      primaryClass={cs.RO},
      url={https://arxiv.org/abs/2504.13059}, 
}
```

RoboTwin: Dual-Arm Robot Benchmark with Generative Digital Twins (**early version**), accepted to <i style="color: red; display: inline;"><b>ECCV Workshop 2024 (Best Paper)</b></i>
```
@article{mu2024robotwin,
  title={RoboTwin: Dual-Arm Robot Benchmark with Generative Digital Twins (early version)},
  author={Mu, Yao and Chen, Tianxing and Peng, Shijia and Chen, Zanxin and Gao, Zeyu and Zou, Yude and Lin, Lunkai and Xie, Zhiqiang and Luo, Ping},
  journal={arXiv preprint arXiv:2409.02920},
  year={2024}
}
```

# 🏷️ License
This repository is released under the MIT license. See [LICENSE](./LICENSE) for additional details.
