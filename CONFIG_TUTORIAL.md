The following is the task configuration for the `apple_cabinet_storage` task, and other tasks are similar.
```
task_name: apple_cabinet_storage
render_freq: 0
eval_video_log: false
use_seed: false
collect_data: true
save_path: ./data
dual_arm: true
st_episode: 0
<!-- head_camera_type: L515 -->
wrist_camera_type: D435
<!-- front_camera_type: D435 -->
pcd_crop: true
pcd_down_sample_num: 1024
episode_num: 100
save_freq: 15
save_type:
  raw_data: false
  pkl: true
data_type:
  rgb: true
  observer: false
  depth: true
  pointcloud: true
  endpose: true
  qpos: true
  mesh_segmentation: false
  actor_segmentation: false
static_camera_list:
- name: head_camera
  type: L515
  position:
  - -0.032
  - -0.45
  - 1.35
  forward:
  - 0
  - 0.6
  - -0.8
  left:
  - -1
  - 0
  - 0
- name: front_camera
  type: D435
  position:
  - 0
  - -0.45
  - 0.85
  forward:
  - 0
  - 1
  - -0.1
  left:
  - -1
  - 0
  - 0
```
Next, we will explain the meaning of each configuration one by one.
## task_name
Task name.

## render_freq
(Default 0) Set to 0 means no visualizatoin. If you wish to visualize the task, it can be set to 10. For off-screen devices, we recommend setting it to 0; otherwise, data collection and testing will be very slow.

## eval_video_log
(Default false) Whether to save visualization video for evaluation.

## use_seed
(Default false) This indicates whether we need to first find `episode_num` successful seeds and then load the corresponding scenarios for the collection task one by one. Setting it to `False` means that we need to first generate `episode_num` successful seeds, while setting it to `True` means directly loading an existing seed list. Generally, the repository clone does not include pre-explored successful seeds, so it is necessary to set this to `False`.

## collect_data
(Default true) Data collection will only be enabled if set to True.

## save_path
(Default ./data) The path for saving data.

## dual_arm
(Default true) Whether to collect and deploy both arms; otherwise, only the right arm will be used. Generally, there is no need to set this to `False`.

## st_episode
(Default 0) Start collecting from which episode, usually set to 0.

<!-- ## head_camera_type & wrist_camera_type & front_camera_type -->
## wrist_camera_type
(Default L515 & D435 & D435) Indicates the camera types used by the head_camera, two wrist_cameras, and front_camera. These are aligned with the real device and can be configured in the [task_config/_camera_config.yml](./task_config/_camera_config.yml). You can also define your own cameras.

```
L515:
  fovy: 45
  w: 320
  h: 180

D435:
  fovy: 37
  w: 320
  h: 240
```

## pcd_crop
(Default true) Determines whether the obtained point cloud data is cropped to remove elements like tables and walls.

## pcd_down_sample_num
(Default 1024) The point cloud data is downsampled using the FPS (Farthest Point Sampling) method, set it to 0 to keep the raw point cloud data.

## episode_num
(Default) 100 Number of data sets you wish to collect.

## save_freq
(Default 15) The frequency of data collection.

## save_type/raw_data
(Default false) Whether to collect data in ARIO format. If you are only training and testing in simulation, it is recommended to set it to `false`.

## save_type/pkl
(Default true) Whether to store the observations at each moment as a pkl file. If training in simulation, it is recommended to set it to `true`.

## data_type/rgb
(Default true) ecides whether to save multi-view RGB photos for easy observation.

## data_type/observer
(Default false) Decides whether to save an observer-view photo for easy observation.

## data_type/depth
(Default true) Decides whether to save multi-view depth maps for easy observation.

## data_type/pointcloud
(Default true) Decides whether to save scene pointcloud for easy observation.

## data_type/endpose
(Default true) Whether to save the 6D pose of the end effector, which still has some minor issues.

## data_type/qpos
(Default true) Whether to save the robot joint state (7 dimensions per arm, 14 dimensions in total).

## data_type/mesh_segmentation
(Default false) Whether to save the mesh segmentation of the RGB observation.

## data_type/actor_segmentation
(Default false) Whether to save the actor segmentation of the RGB observation.

## static_camera_list

@TODO
