import os
import hydra
import torch
import dill
from omegaconf import OmegaConf
import pathlib
import sys
current_file_path = os.path.abspath(__file__)
current_folder_path = os.path.dirname(current_file_path)
parent_directory = os.path.dirname(current_file_path)
sys.path.append(os.path.join(current_folder_path, './3D-Diffusion-Policy'))
path_to_remove = os.path.abspath(os.path.join(parent_directory, '../Diffusion-Policy'))
if path_to_remove in sys.path:
    sys.path.remove(path_to_remove)
from train import TrainDP3Workspace
import pdb
from pathlib import Path
import yaml

class DP3:
    def __init__(self, cfg, ckpt_file) -> None:
        self.policy, self.env_runner = self.get_policy_and_runner(cfg, ckpt_file)
        
    def update_obs(self, observation):
        self.env_runner.update_obs(observation)
    
    def get_action(self, observation=None):
        action = self.env_runner.get_action(self.policy, observation)
        return action    

    def get_policy_and_runner(self, cfg, ckpt_file):
        workspace = TrainDP3Workspace(cfg)
        policy, env_runner = workspace.get_policy_and_runner(cfg, ckpt_file)
        return policy, env_runner

def encode_obs(observation):
    res = dict()
    res['point_cloud'] = observation['pointcloud']
    res['agent_pos'] = observation['joint_action']
    return res

def get_model(ckpt_folder, task_name):
    print('ckpt_folder: ', ckpt_folder)
    ckpt_file = os.path.join(ckpt_folder, '3000.ckpt')
    config_file_path = Path(__file__).parent.joinpath(f'3D-Diffusion-Policy/diffusion_policy_3d/config/robot_dp3.yaml')
    with open(config_file_path, 'r') as config_file:
        config = yaml.safe_load(config_file)
    task_config_path = Path(__file__).parent.joinpath(f'3D-Diffusion-Policy/diffusion_policy_3d/config/task/{task_name}.yaml')
    with open(task_config_path, 'r') as task_config:
        config['task'] = yaml.safe_load(task_config)
    config = OmegaConf.create(config)
    return DP3(config, ckpt_file)

def eval(TASK_ENV, model, observation):
    '''
    TASK_ENV: Task Environment Class, you can use this class to interact with the environment
    model: The model from 'get_model()' function
    observation: The observation about the environment 
    '''
    obs = encode_obs(observation)
    # ======== Get Action ========

    if len(model.env_runner.obs) == 0:
        model.update_obs(obs)    
    actions = model.get_action()

    for action in actions:
        TASK_ENV.take_action(action)
        observation = TASK_ENV.get_obs()
        obs = encode_obs(observation)
        model.update_obs(obs)

def reset_model(model):
    model.env_runner.reset_obs()