import sapien.core as sapien
from sapien.utils.viewer import Viewer
import numpy as np
import gymnasium as gym
import pdb
import numpy as np
import toppra as ta
import json
import transforms3d as t3d
from collections import OrderedDict

from .utils import *
import math
from .robot import Robot
from .camera import Camera
import random
from copy import deepcopy
import subprocess
from pathlib import Path
import trimesh

current_file_path = os.path.abspath(__file__)
parent_directory = os.path.dirname(current_file_path)

TACTILE_ON = os.environ.get('VISION_TACTILE_ON', '0') == '1'
if TACTILE_ON:
    import warp as wp
    from .camera.vision_tactile_sensor import VisionTactileSensors
    from sapienipc.ipc_utils.user_utils import ipc_update_render_all
    from sapienipc.ipc_system import IPCSystem, IPCSystemConfig
    from sapienipc.ipc_component import IPCFEMComponent, IPCABDComponent
    
    # set device
    wp.init()
    device = wp.get_preferred_device()

class Base_task(gym.Env):

    # DEFAULT_ACTOR_DATA = {
    #     "scale": [1,1,1],
    #     "target_pose": [
    #         [1,0,0,0],
    #         [0,1,0,0],
    #         [0,0,1,0],
    #         [0,0,0,1]
    #     ],
    #     "contact_pose": [[
    #         [1,0,0,0],
    #         [0,1,0,0],
    #         [0,0,1,0],
    #         [0,0,0,1]]
    #     ],
    #     "trans_matrix": [
    #         [1,0,0,0],
    #         [0,1,0,0],
    #         [0,0,1,0],
    #         [0,0,0,1]
    #     ]
    # }
    def __init__(self):
        pass

    def _init(self, **kwags):
        '''
            Initialization
            - `self.PCD_INDEX`: The index of the file saved for the current scene.
            - `self.fcitx5-configtool`: Left gripper pose (close <=0, open >=0.4).
            - `self.ep_num`: Episode ID.
            - `self.task_name`: Task name.
            - `self.save_dir`: Save path.`
            - `self.left_original_pose`: Left arm original pose.
            - `self.right_original_pose`: Right arm original pose.
            - `self.left_arm_joint_id`: [6,14,18,22,26,30].
            - `self.right_arm_joint_id`: [7,15,19,23,27,31].
            - `self.render_fre`: Render frequency.
        '''
        super().__init__()
        ta.setup_logging("CRITICAL") # hide logging
        np.random.seed(kwags.get('seed', 0))

        self.PCD_INDEX = 0
        self.task_name = kwags.get('task_name')
        self.save_dir = kwags.get('save_path', 'data')
        self.ep_num = kwags.get('now_ep_num', 0)
        self.render_freq = kwags.get('render_freq', 10)
        self.random_texture = kwags.get('random_texture', False)
        self.data_type = kwags.get('data_type', None)
        self.is_save = kwags.get('is_save', False)
        self.dual_arm = kwags.get('dual_arm', True)
        self.table_static = kwags.get('table_static', True)
        self.messy_table = kwags.get('messy_table', False)

        self.file_path = []
        self.plan_success = True
        self.step_lim = None
        self.fix_gripper = False
        self.setup_scene()

        self.left_js = None
        self.right_js = None
        self.raw_head_pcl = None
        self.real_head_pcl = None
        self.real_head_pcl_color = None

        self.now_obs = {}
        self.take_action_cnt = 0
        self.eval_video_path = kwags.get('eval_video_save_dir', None)
        self.grasp_direction_dic = {
            'left':         [0,      0,   0,    -1],
            'front_left':   [-0.383, 0,   0,    -0.924],
            'front' :       [-0.707, 0,   0,    -0.707],
            'front_right':  [-0.924, 0,   0,    -0.383],
            'right':        [-1,     0,   0,    0],
            'top_down':     [-0.5,   0.5, -0.5, -0.5],
        }

        self.world_direction_dic = {
            'left':         [0.5,  0.5,  0.5,  0.5],
            'front_left':   [0.65334811, 0.27043713, 0.65334811, 0.27043713],
            'front' :       [0.707, 0,    0.707, 0],
            'front_right':  [0.65334811, -0.27043713,  0.65334811, -0.27043713],
            'right':        [0.5,    -0.5, 0.5,  0.5],
            'top_down':     [0,      0,   1,    0],
        }
        
        self.save_freq = kwags.get('save_freq')
        self.world_pcd = None
        # left_pub_data = [0,0,0,0,0,0,0]
        # right_pub_data = [0,0,0,0,0,0,0]

        self.size_dict = list()
        self.messy_objs = list()
        self.prohibited_area = list() # [x1, y1,x2, y2]
        self.record_messy_objects = list() # record messy objects

        self.eval_success_cvpr = False
        self.cvpr_score = 0 
        self.eval_video_ffmpeg = None

    def _del_eval_video_ffmpeg(self):
        if self.eval_video_ffmpeg is not None:
            self.eval_video_ffmpeg.stdin.close()
            self.eval_video_ffmpeg.wait()
            del self.eval_video_ffmpeg 
             
    def _set_eval_video_ffmpeg(self,  ffmpeg):
        self.eval_video_ffmpeg = ffmpeg 

    def play_once(self):
        pass
    
    def check_success(self):
        pass

    def pre_move(self):
        pass

    def setup_scene(self,**kwargs):
        '''
        Set the scene
            - Set up the basic scene: light source, viewer.
        '''
        self.engine = sapien.Engine()
        # declare sapien renderer
        from sapien.render import set_global_config
        set_global_config(max_num_materials = 50000, max_num_textures = 50000)
        self.renderer = sapien.SapienRenderer()
        # give renderer to sapien sim
        self.engine.set_renderer(self.renderer)
        
        sapien.render.set_camera_shader_dir("rt")
        sapien.render.set_ray_tracing_samples_per_pixel(32)
        sapien.render.set_ray_tracing_path_depth(8)
        sapien.render.set_ray_tracing_denoiser("oidn")

        # declare sapien scene
        scene_config = sapien.SceneConfig()
        self.scene = self.engine.create_scene(scene_config)
        # set simulation timestep
        self.scene.set_timestep(kwargs.get("timestep", 1 / 250))
        # add ground to scene
        self.scene.add_ground(kwargs.get("ground_height", 0))
        # set default physical material
        self.scene.default_physical_material = self.scene.create_physical_material(
            kwargs.get("static_friction", 0.5),
            kwargs.get("dynamic_friction", 0.5),
            kwargs.get("restitution", 0),
        )
        # give some white ambient light of moderate intensity
        self.scene.set_ambient_light(kwargs.get("ambient_light", [0.5, 0.5, 0.5]))
        # default enable shadow unless specified otherwise
        shadow = kwargs.get("shadow", True)
        # default spotlight angle and intensity
        direction_lights = kwargs.get(
            "direction_lights", [[[0, 0.5, -1], [0.5, 0.5, 0.5]]]
        )
        for direction_light in direction_lights:
            self.scene.add_directional_light(
                direction_light[0], direction_light[1], shadow=shadow
            )
        # default point lights position and intensity
        point_lights = kwargs.get(
            "point_lights",
            [[[1, 0, 1.8], [1, 1, 1]], [[-1, 0, 1.8], [1, 1, 1]]]
        )
        for point_light in point_lights:
            self.scene.add_point_light(point_light[0], point_light[1], shadow=shadow)

        # initialize viewer with camera position and orientation
        if self.render_freq:
            self.viewer = Viewer(self.renderer)
            self.viewer.set_scene(self.scene)
            self.viewer.set_camera_xyz(
                x=kwargs.get("camera_xyz_x", 0.4),
                y=kwargs.get("camera_xyz_y", 0.22),
                z=kwargs.get("camera_xyz_z", 1.5),
            )
            self.viewer.set_camera_rpy(
                r=kwargs.get("camera_rpy_r", 0),
                p=kwargs.get("camera_rpy_p", -0.8),
                y=kwargs.get("camera_rpy_y", 2.45),
            )
        
        if TACTILE_ON:
            if not hasattr(self, 'ipc_system'):
                # create ipc system
                ipc_system_config = IPCSystemConfig()
                ipc_system_config.device = device
                # memory config
                ipc_system_config.max_scenes = 1
                ipc_system_config.max_particles = 50000
                ipc_system_config.max_surface_primitives_per_scene = 500000
                ipc_system_config.max_blocks = 4000000
                # scene config
                ipc_system_config.time_step = 0.05
                ipc_system_config.gravity = wp.vec3(
                    self.scene.physx_system.get_config().gravity)
                ipc_system_config.d_hat = 2e-4
                ipc_system_config.eps_d = 1e-4
                ipc_system_config.eps_v = 1e-3
                ipc_system_config.v_max = 1e-1
                ipc_system_config.kappa = 1e3
                ipc_system_config.kappa_affine = 1e5
                ipc_system_config.kappa_con = 1e10
                ipc_system_config.ccd_slackness = 0.7
                ipc_system_config.ccd_thickness = 0.0
                ipc_system_config.ccd_tet_inversion_thres = 0.0
                ipc_system_config.ee_classify_thres = 1e-3
                ipc_system_config.ee_mollifier_thres = 1e-3
                ipc_system_config.allow_self_collision = False

                # # solver config
                ipc_system_config.newton_max_iters = 4  # key param
                ipc_system_config.cg_max_iters = 50
                ipc_system_config.line_search_max_iters = 10
                ipc_system_config.ccd_max_iters = 10
                ipc_system_config.precondition = "jacobi"
                ipc_system_config.cg_error_tolerance = 1e-4
                ipc_system_config.cg_error_frequency = 10
                ipc_system_config.debug = False
                self.ipc_system = IPCSystem(ipc_system_config)
            else:
                self.ipc_system.components.clear()
                self.ipc_system.rebuild()
            self.ipc_step = 0
            self.ipc_fail = False
            self.scene.add_system(self.ipc_system)

    def create_table_and_wall(self, table_pose = [0,0], table_height = 0.74):
        # creat wall
        wall_texture, table_texture = None, None
        
        self.wall_texture, self.table_texture = 0, 0
            
        self.wall = create_box(
            self.scene,
            sapien.Pose(p=[0, 1, 1.5]),
            half_size=[3, 0.6, 1.5],
            color=(1, 0.9, 0.9), 
            name='wall',
            is_static=True,
            texture_id=wall_texture
        )
        
        if TACTILE_ON:
            # transparent wall to fix the bug when sapien camera get deep photo
            wall2 = create_box(
                self.scene,
                sapien.Pose(p=[2.9, -1.62, 1.5]),
                half_size=[0.1, 2, 1.5],
                color=(0, 0, 0, 0),
                name='wall_right',
                is_static=True,
            )
            wall3 = create_box(
                self.scene,
                sapien.Pose(p=[-2.9, -1.62, 1.5]),
                half_size=[0.1, 2, 1.5],
                color=(0, 0, 0, 0), 
                name='wall_left',
                is_static=True,
            )
            wall4 = create_box(
                self.scene,
                sapien.Pose(p=[0, -3.74, 1.5]),
                half_size=[3, 0.1, 1.5],
                color=(0, 0, 0, 0), 
                name='wall_back',
                is_static=True,
            )

        # creat table
        self.table = create_table(
            self.scene,
            sapien.Pose(p = [table_pose[0], table_pose[1], table_height]),
            length=1.2,
            width=0.7,
            height=table_height,
            thickness=0.05,
            is_static=self.table_static,
            texture_id=table_texture
        )

    def load_robot(self, **kwags):
        """
            load aloha robot urdf file, set root pose and set joints
        """

        self.robot = Robot(self.scene, **kwags)

        self.robot.set_planner()
        # self.robot.set_planner(self.scene)
        self.robot.init_joints()

        for link in self.robot.left_entity.get_links():
            link:sapien.physx.PhysxArticulationLinkComponent = link
            link.set_mass(1)
        for link in self.robot.right_entity.get_links():
            link:sapien.physx.PhysxArticulationLinkComponent = link
            link.set_mass(1)
    
    def load_camera(self, **kwags):
        '''
            Add cameras and set camera parameters
                - Including four cameras: left, right, front, head.
        '''

        self.cameras = Camera(**kwags)
        self.cameras.load_camera(self.scene)
        
        if TACTILE_ON:
            self.vsensors = VisionTactileSensors(**kwags)
            self.vsensors.load_sensor(
                self.scene,
                self.ipc_system,
                self.robot.left_entity.get_links()
            )
        
        self.scene.step()  # run a physical step
        if TACTILE_ON:
            self.ipc_system.step()
            ipc_update_render_all(self.scene)
        self.scene.update_render()  # sync pose from SAPIEN to renderer

        # TODO
        # self.world_pcd = self.cameras.get_world_pcd()
        # save_pcd('./test_world_pcd.pcd', self.world_pcd, color=True)
        # self.robot.update_world_pcd(self.world_pcd[:,:3])

    def _update_render(self):
        """
            Update rendering to refresh the camera's RGBD information 
            (rendering must be updated even when disabled, otherwise data cannot be collected).
        """
        self.cameras.update_wrist_camera(self.robot.left_camera.get_pose(), self.robot.right_camera.get_pose())
        
        if TACTILE_ON:
            ipc_update_render_all(self.scene)
        self.scene.update_render()
    
    def _step(self):
        '''
            pipeline:
                physx_system.step() ->
                sensor.step() -> ipc_system.step() ->
                obj.step() ->
                ipc_update_render_all(scene) -> scene.update_render() ->
                viewer.render()
        '''
        self.scene.step()
        if TACTILE_ON:
            ret = self.vsensors.update_sensors()
            if ret['status'] == 'fail':
                self.ipc_fail = True
            if ret['loaded']:
                self.ipc_step += 1
    
    # 延时操作
    def delay(self, delay_time):
        render_freq = self.render_freq
        self.render_freq=0
        left_gripper_val = self.robot.get_left_gripper_val()
        right_gripper_val = self.robot.get_right_gripper_val()
        for i in range(delay_time):
            self.together_close_gripper(left_pos=left_gripper_val, right_pos=right_gripper_val)
        self.render_freq = render_freq

    def set_gripper(self, set_tag = 'together', left_pos = None, right_pos = None, save_freq=-1):
        '''
            Set gripper posture
            - `left_pos`: Left gripper pose
            - `right_pos`: Right gripper pose
            - `set_tag`: "left" to set the left gripper, "right" to set the right gripper, "together" to set both grippers simultaneously.
        '''
        save_freq = self.save_freq if save_freq == -1 else save_freq
        if save_freq != None:
            self._take_picture()
        
        left_gripper_step = 0
        right_gripper_step = 0
        step_n = 0
        if set_tag == 'left' or set_tag == 'together':
            # left_result = self.robot.left_planner.plan_grippers(self.robot.get_left_gripper_real_val(), left_pos)
            left_result = self.robot.left_planner.plan_grippers(self.robot.get_left_gripper_val(), left_pos)
            left_gripper_step = left_result['step']
            left_gripper_res = left_result['result']
            step_n = left_result['step_n']

        if set_tag == 'right' or set_tag == 'together':
            # right_result = self.robot.right_planner.plan_grippers(self.robot.get_right_gripper_real_val(), right_pos)
            right_result = self.robot.right_planner.plan_grippers(self.robot.get_right_gripper_val(), right_pos)
            right_gripper_step = right_result['step']
            right_gripper_res = right_result['result']
            step_n = right_result['step_n']

        alpha = 1.5
        # for i in range(int(step_n * 3 // 2)): # TODO
        for i in range(int(step_n * alpha)):
            if set_tag == 'left' or set_tag == 'together':
                self.robot.set_gripper(left_gripper_res[min(i, step_n-1)], 'left', left_gripper_step)

            if set_tag == 'right' or set_tag == 'together':
                self.robot.set_gripper(right_gripper_res[min(i, step_n-1)], 'right', right_gripper_step)

            self._step()
            if self.render_freq and i % self.render_freq == 0:
                self._update_render()
                self.viewer.render()

            if save_freq != None and i % save_freq == 0:
                self._update_render()
                self._take_picture()

        if save_freq != None:
            self._take_picture()
        
    def open_left_gripper(self, save_freq=-1, pos = 1):
        self.set_gripper(left_pos = pos, set_tag='left', save_freq=save_freq)

    def close_left_gripper(self, save_freq=-1, pos = 0.15):
        self.set_gripper(left_pos = pos, set_tag='left',save_freq=save_freq)

    def open_right_gripper(self, save_freq=-1,pos = 1):
        self.set_gripper(right_pos=pos, set_tag='right', save_freq=save_freq)

    def close_right_gripper(self, save_freq=-1,pos = 0.15):
        self.set_gripper(right_pos=pos, set_tag='right', save_freq=save_freq)

    def together_open_gripper(self, save_freq=-1, left_pos = 1, right_pos = 1):
        self.set_gripper(left_pos=left_pos, right_pos=right_pos, set_tag='together', save_freq=save_freq)

    def together_close_gripper(self, save_freq=-1,left_pos = 0.15, right_pos = 0.15):
        self.set_gripper(left_pos=left_pos, right_pos=right_pos, set_tag='together', save_freq=save_freq)
        
    def left_move_to_pose(self, pose, use_point_cloud=False, use_attach=False,save_freq=-1):
        """
        Interpolative planning with screw motion.
        Will not avoid collision and will fail if the path contains collision.
        """
        save_freq = self.save_freq if save_freq == -1 else save_freq
        left_result = self.robot.left_plan_path(pose, use_point_cloud, use_attach)

        if left_result["status"] != "Success":
            self.plan_success = False
            return
        
        # print('target: ',pose)
        if save_freq != None:
            self._take_picture()

        n_step = left_result["position"].shape[0]
        for i in range(n_step):
            self.robot.set_arm_joints(left_result['position'][i], left_result['velocity'][i], 'left')
            self._step()
            # if i%5 == 0:
            if self.render_freq and i % self.render_freq == 0:
                self._update_render()
                self.viewer.render()
            
            if save_freq != None and i % save_freq == 0:
                self._update_render()
                self._take_picture()

        # print('real: ', self.robot.get_left_ee_pose())
        if save_freq != None:
            self._take_picture()

    def right_move_to_pose(self, pose, use_point_cloud=False, use_attach=False, save_freq=-1):
        """
        Interpolative planning with screw motion.
        Will not avoid collision and will fail if the path contains collision.
        """
        save_freq = self.save_freq if save_freq == -1 else save_freq
        right_result = self.robot.right_plan_path(pose, use_point_cloud, use_attach)

        if right_result["status"] != "Success":
            self.plan_success = False
            return
        
        if save_freq != None:
            self._take_picture()

        # print('target: ',pose)
        n_step = right_result["position"].shape[0]
        for i in range(n_step):
            self.robot.set_arm_joints(right_result['position'][i], right_result['velocity'][i], 'right')
            self._step()
            if self.render_freq and i % self.render_freq == 0:
                self._update_render()
                self.viewer.render()
            
            if save_freq != None and i % save_freq == 0:
                self._update_render()
                self._take_picture()

        # print('real: ', self.robot.get_right_ee_pose())
        if save_freq != None:
            self._take_picture()

    def together_move_to_pose(self, left_target_pose, right_target_pose, use_point_cloud=False, use_attach=False, save_freq=-1):
        """
        Interpolative planning with screw motion.
        Will not avoid collision and will fail if the path contains collision.
        """
        save_freq = self.save_freq if save_freq == -1 else save_freq
        left_result = self.robot.left_plan_path(left_target_pose, use_point_cloud, use_attach)
        right_result = self.robot.right_plan_path(right_target_pose, use_point_cloud, use_attach)

        left_success = left_result["status"] == "Success"
        right_success = right_result["status"] == "Success"
        if not left_success or not right_success:
            self.plan_success = False
            # return
        
        if save_freq != None:
            self._take_picture()

        now_left_id = 0
        now_right_id = 0
        i = 0

        left_n_step = left_result["position"].shape[0] if left_success else 0
        right_n_step = right_result["position"].shape[0] if right_success else 0

        while now_left_id < left_n_step or now_right_id < right_n_step:
            # set the joint positions and velocities for move group joints only.
            # The others are not the responsibility of the planner
            if left_success and now_left_id < left_n_step and (not right_success or now_left_id / left_n_step <= now_right_id / right_n_step):
                self.robot.set_arm_joints(left_result['position'][now_left_id], left_result['velocity'][now_left_id], 'left')
                now_left_id +=1
                
            if right_success and now_right_id < right_n_step and (not left_success or now_right_id / right_n_step <= now_left_id / left_n_step):
                self.robot.set_arm_joints(right_result['position'][now_right_id], right_result['velocity'][now_right_id], 'right')
                now_right_id +=1

            self._step()
            if self.render_freq and i % self.render_freq == 0:
                self._update_render()
                self.viewer.render()

            if save_freq != None and i % save_freq == 0:
                self._update_render()
                self._take_picture()
            i+=1

        if save_freq != None:
            self._take_picture()
    
    def is_left_gripper_open(self):
        return self.robot.is_left_gripper_open()
    
    def is_right_gripper_open(self):
        return self.robot.is_right_gripper_open()
    
    def is_left_gripper_open_half(self):
        return self.robot.is_left_gripper_open_half()
    
    def is_right_gripper_open_half(self):
        return self.robot.is_right_gripper_open_half()
    
    def is_left_gripper_close(self):
        return self.robot.is_left_gripper_close()
    
    def is_right_gripper_close(self):
        return self.robot.is_right_gripper_close()

    # =========================================================== New APIS ===========================================================
    def get_target_pose_from_goal_point_and_gripper_direction(self, actor, actor_data = None, arm_tag = None, target_pose = None, target_grasp_qpose = None):
        """
            Obtain the grasp pose through the given target point and contact direction.
            - actor: The instance of the object to be grasped.
            - actor_data: The annotation data corresponding to the instance of the object to be grasped.
            - endpose: The end effector pose, from robot.get_left_ee_pose() or robot.get_right_ee_pose().
            - target_pose: The target point coordinates for aligning the functional points of the object to be grasped.
            - target_grasp_qpose: The direction of the grasped object's contact target point, 
                                 represented as a quaternion in the world coordinate system.
        """
        endpose = self.robot.get_left_ee_pose() if arm_tag == 'left' else self.robot.get_right_ee_pose()
        actor_matrix = actor.get_pose().to_transformation_matrix()
        local_target_matrix = np.asarray(actor_data['target_pose'][0])
        local_target_matrix[:3,3] *= actor_data['scale']
        res_matrix = np.eye(4)
        res_matrix[:3,3] = (actor_matrix  @ local_target_matrix)[:3,3] - endpose[:3]
        # @ np.array([[1,0,0],[0,-1,0],[0,0,-1]])
        res_matrix[:3,3] = np.linalg.inv(t3d.quaternions.quat2mat(endpose[-4:])) @ res_matrix[:3,3]
        res_pose = list(target_pose - t3d.quaternions.quat2mat(target_grasp_qpose) @ res_matrix[:3,3]) + target_grasp_qpose
        return res_pose

    def get_grasp_pose_w_labeled_direction(self, actor, actor_data, pre_dis = 0., contact_point_id = 0):
        """
            Obtain the grasp pose through the marked grasp point.
            - actor: The instance of the object to be grasped.
            - actor_data: The annotation data corresponding to the instance of the object to be grasped.
            - pre_dis: The distance in front of the grasp point.
            - contact_point_id: The index of the grasp point.
        """
        actor_matrix = actor.get_pose().to_transformation_matrix()
        local_contact_matrix = np.asarray(actor_data['contact_points_pose'][contact_point_id])
        local_contact_matrix[:3,3] *= actor_data['scale']
        global_contact_pose_matrix = actor_matrix  @ local_contact_matrix @ np.array([[0, 0, 1, 0],
                                                                                      [-1,0, 0, 0],
                                                                                      [0, -1,0, 0],
                                                                                      [0, 0, 0, 1]])
        global_contact_pose_matrix_q = global_contact_pose_matrix[:3,:3]
        global_grasp_pose_p = global_contact_pose_matrix[:3,3] + global_contact_pose_matrix_q @ np.array([-0.12-pre_dis,0,0]).T
        global_grasp_pose_q = t3d.quaternions.mat2quat(global_contact_pose_matrix_q)
        res_pose = list(global_grasp_pose_p)+list(global_grasp_pose_q)
        return res_pose
    
    def get_grasp_pose_from_goal_point_and_direction(self, actor, actor_data,  endpose_tag: str, actor_functional_point_id = 0, target_point = None,
                                                     target_approach_direction = [0,0,1,0], actor_target_orientation = None, pre_dis = 0.):
        """
            Obtain the grasp pose through the given target point and contact direction.
            - actor: The instance of the object to be grasped.
            - actor_data: The annotation data corresponding to the instance of the object to be grasped.
            - endpose_tag: Left and right gripper marks, with values "left" or "right".
            - actor_functional_point_id: The index of the functional point to which the object to be grasped needs to be aligned.
            - target_point: The target point coordinates for aligning the functional points of the object to be grasped.
            - target_approach_direction: The direction of the grasped object's contact target point, 
                                         represented as a quaternion in the world coordinate system.
            - actor_target_orientation: The final target orientation of the object, 
                                        represented as a direction vector in the world coordinate system.
            - pre_dis: The distance in front of the grasp point.
        """
        target_approach_direction_mat = t3d.quaternions.quat2mat(target_approach_direction)
        actor_matrix = actor.get_pose().to_transformation_matrix()
        target_point_copy = deepcopy(target_point[:3])
        target_point_copy -= target_approach_direction_mat @ np.array([0,0,pre_dis])

        try:
            actor_orientation_point = np.array(actor_data['orientation_point'])[:3,3]
        except:
            actor_orientation_point = [0,0,0]

        if actor_target_orientation is not None:
            actor_target_orientation = actor_target_orientation / np.linalg.norm(actor_target_orientation)

        end_effector_pose = self.robot.get_left_ee_pose() if endpose_tag == 'left' else self.robot.get_right_ee_pose()
        res_pose = None
        # res_eval= -1e10
        # for adjunction_matrix in adjunction_matrix_list:
        local_target_matrix = np.asarray(actor_data['functional_matrix'][actor_functional_point_id])
        local_target_matrix[:3,3] *= actor_data['scale']
        fuctional_matrix = actor_matrix[:3,:3] @ np.asarray(actor_data['functional_matrix'][actor_functional_point_id])[:3,:3]
        # fuctional_matrix = fuctional_matrix @ adjunction_matrix
        trans_matrix = target_approach_direction_mat @ np.linalg.inv(fuctional_matrix)
        #  @ np.array([[1,0,0],[0,-1,0],[0,0,-1]])
        ee_pose_matrix = t3d.quaternions.quat2mat(end_effector_pose[-4:])
        target_grasp_matrix = trans_matrix @ ee_pose_matrix

        # Use actor target orientation to filter
        if actor_target_orientation is not None:
            now_actor_orientation_point = trans_matrix @ actor_matrix[:3,:3] @ np.array(actor_orientation_point)
            now_actor_orientation_point = now_actor_orientation_point / np.linalg.norm(now_actor_orientation_point)
            # produt = np.dot(now_actor_orientation_point, actor_target_orientation)
            # # The difference from the target orientation is too large
            # if produt < 0.8:
            #     continue
        
        res_matrix = np.eye(4)
        res_matrix[:3,3] = (actor_matrix  @ local_target_matrix)[:3,3] - end_effector_pose[:3]
        res_matrix[:3,3] = np.linalg.inv(ee_pose_matrix) @ res_matrix[:3,3]
        target_grasp_qpose = t3d.quaternions.mat2quat(target_grasp_matrix)
        # priget_grasp_pose_w_labeled_directionnt(target_grasp_matrix @ res_matrix[:3,3])
        res_pose = (target_point_copy - target_grasp_matrix @ res_matrix[:3,3]).tolist() + target_grasp_qpose.tolist()
        return res_pose
    
    # Get the pose coordinates of the actor's target point in the world coordinate system.
    # Return value: [x, y, z]
    def get_actor_goal_pose(self,actor,actor_data, id = 0):
        if type(actor) == list:
            return actor
        actor_matrix = actor.get_pose().to_transformation_matrix()
        local_target_matrix = np.asarray(actor_data['target_pose'][id])
        local_target_matrix[:3,3] *= actor_data['scale']
        return (actor_matrix @ local_target_matrix)[:3,3]

    # Get the actor's functional point and axis corresponding to the index in the world coordinate system.
    # Return value: [x, y, z, quaternion].
    def get_actor_functional_pose(self, actor, actor_data, actor_functional_point_id = 0):
        if type(actor) == list:
            return actor
        actor_matrix = actor.get_pose().to_transformation_matrix()
        # if "model_type" in actor_data.keys() and actor_data["model_type"] == "urdf": actor_matrix[:3,:3] = self.URDF_MATRIX
        local_functional_matrix = np.asarray(actor_data['functional_matrix'][actor_functional_point_id])
        local_functional_matrix[:3,3] *= actor_data['scale']
        res_matrix = actor_matrix @ local_functional_matrix
        return res_matrix[:3,3].tolist() + t3d.quaternions.mat2quat(res_matrix[:3,:3]).tolist()

    # Get the actor's grasp point and axis corresponding to the index in the world coordinate system.
    # Return value: [x, y, z, quaternion]
    def get_actor_contact_point_position(self, actor, actor_data, actor_contact_id = 0):
        if type(actor) == list:
            return actor
        actor_matrix = actor.get_pose().to_transformation_matrix()
        # if "model_type" in actor_data.keys() and actor_data["model_type"] == "urdf": actor_matrix[:3,:3] = self.URDF_MATRIX
        local_contact_matrix = np.asarray(actor_data['contact_points_pose'][actor_contact_id])
        local_contact_matrix[:3,3] *= actor_data['scale']
        res_matrix = actor_matrix @ local_contact_matrix
        return res_matrix[:3,3].tolist() + t3d.quaternions.mat2quat(res_matrix[:3,:3]).tolist()
    # =========================================================== New APIS ===========================================================
    

    # =========================================================== Old APIS ===========================================================
    # def get_grasp_pose_w_labeled_direction(self, actor, actor_data = DEFAULT_ACTOR_DATA, grasp_matrix = np.eye(4), pre_dis = 0, id = 0):
    #     actor_matrix = actor.get_pose().to_transformation_matrix()
    #     local_contact_matrix = np.asarray(actor_data['contact_pose'][id])
    #     trans_matrix = np.asarray(actor_data['trans_matrix'])
    #     local_contact_matrix[:3,3] *= actor_data['scale']
    #     global_contact_pose_matrix = actor_matrix  @ local_contact_matrix @ trans_matrix @ grasp_matrix @ np.array([[0,0,1,0],[-1,0,0,0],[0,-1,0,0],[0,0,0,1]])
    #     global_contact_pose_matrix_q = global_contact_pose_matrix[:3,:3]
    #     global_grasp_pose_p = global_contact_pose_matrix[:3,3] + global_contact_pose_matrix_q @ np.array([-0.12-pre_dis,0,0]).T
    #     global_grasp_pose_q = t3d.quaternions.mat2quat(global_contact_pose_matrix_q)
    #     res_pose = list(global_grasp_pose_p)+list(global_grasp_pose_q)
    #     return res_pose

    # def get_grasp_pose_w_given_direction(self,actor,actor_data = DEFAULT_ACTOR_DATA,grasp_qpos: list = None, pre_dis = 0, id = 0):
    #     actor_matrix = actor.get_pose().to_transformation_matrix()
    #     local_contact_matrix = np.asarray(actor_data['contact_pose'][id])
    #     local_contact_matrix[:3,3] *= actor_data['scale']
    #     grasp_matrix= t3d.quaternions.quat2mat(grasp_qpos)
    #     global_contact_pose_matrix = actor_matrix @ local_contact_matrix
    #     global_grasp_pose_p = global_contact_pose_matrix[:3,3] + grasp_matrix @ np.array([-0.12-pre_dis,0,0]).T
    #     res_pose = list(global_grasp_pose_p) + grasp_qpos
    #     return res_pose

    # def get_target_pose_from_goal_point_and_gripper_direction(self, actor, actor_data = DEFAULT_ACTOR_DATA, ee_pose = None, target_pose = None, target_grasp_qpose = None):
    #     actor_matrix = actor.get_pose().to_transformation_matrix()
    #     local_target_matrix = np.asarray(actor_data['target_pose'])
    #     local_target_matrix[:3,3] *= actor_data['scale']
    #     res_matrix = np.eye(4)
    #     res_matrix[:3,3] = (actor_matrix  @ local_target_matrix)[:3,3] - ee_pose[:3]
    #     res_matrix[:3,3] = np.linalg.inv(t3d.quaternions.quat2mat(ee_pose[-4:])) @ res_matrix[:3,3]
    #     res_pose = list(target_pose - t3d.quaternions.quat2mat(target_grasp_qpose) @ res_matrix[:3,3]) + target_grasp_qpose
    #     return res_pose
    
    # def get_actor_goal_pose(self,actor,actor_data = DEFAULT_ACTOR_DATA):
    #     actor_matrix = actor.get_pose().to_transformation_matrix()
    #     local_target_matrix = np.asarray(actor_data['target_pose'])
    #     local_target_matrix[:3,3] *= actor_data['scale']
    #     return (actor_matrix @ local_target_matrix)[:3,3]
        
    # =========================================================== Old APIS ===========================================================

    def _take_picture(self): # Save data
        if not self.is_save:
            return
        print('saving: episode = ', self.ep_num, ' index = ',self.PCD_INDEX, end='\r')

        if self.PCD_INDEX==0:
            self.file_path ={
                "pkl" : f"{self.save_dir}/episode{self.ep_num}/",
            }

            for directory in self.file_path.values():
                if os.path.exists(directory):
                    file_list = os.listdir(directory)
                    for file in file_list:
                        os.remove(directory + file)
        pkl_dic = self.get_obs(is_policy=False)
        save_pkl(self.file_path["pkl"]+f"{self.PCD_INDEX}.pkl", pkl_dic)
        self.PCD_INDEX +=1
    
    def get_obs(self, is_policy = True):
        self._update_render()
        self.cameras.update_picture()
        if TACTILE_ON:
            self.vsensors.update_picture()
        pkl_dic = {
            "observation":{
                # "left_camera":{},
                # "right_camera":{},
                # "head_camera":{},   # rbg , mesh_seg , actior_seg , depth , intrinsic_cv , extrinsic_cv , cam2world_gl(model_matrix)
                # "front_camera":{}
            },
            "pointcloud":[],   # conbinet pcd
            "joint_action":[],
            "endpose":[]
        }
        
        if TACTILE_ON:
            pkl_dic['vision_tactile'] = self.vsensors.get_config()
            # # ---------------------------------------------------------------------------- #
            # # Tactile Sensors
            # # ---------------------------------------------------------------------------- #
            if self.data_type.get('vision_tactile', True):
                rgb = self.vsensors.get_rgb()
                # rgb = self.vsensors.get_debug()
                for sensor_name in rgb.keys():
                    pkl_dic['vision_tactile'][sensor_name].update(rgb[sensor_name])

        pkl_dic['observation'] = self.cameras.get_config()
        # # ---------------------------------------------------------------------------- #
        # # RGBA
        # # ---------------------------------------------------------------------------- #
        if self.data_type.get('rgb', False):
            rgba = self.cameras.get_rgba()
            for camera_name in rgba.keys():
                pkl_dic['observation'][camera_name].update(rgba[camera_name])
        
        if self.data_type.get('observer', False):
            observer = self.cameras.get_obs_rgba()
            pkl_dic['obs_rgba'] = observer
        # # ---------------------------------------------------------------------------- #
        # # mesh_segmentation
        # # ---------------------------------------------------------------------------- # 
        if self.data_type.get('mesh_segmentation', False):
            mesh_segmentation = self.cameras.get_segmentation(level='mesh')
            for camera_name in mesh_segmentation.keys():
                pkl_dic['observation'][camera_name].update(mesh_segmentation[camera_name])
        # # ---------------------------------------------------------------------------- #
        # # actor_segmentation
        # # --------------------------------------------------------------------------- # 
        if self.data_type.get('actor_segmentation', False):
            actor_segmentation = self.cameras.get_segmentation(level='actor')
            for camera_name in actor_segmentation.keys():
                pkl_dic['observation'][camera_name].update(actor_segmentation[camera_name])
        # # ---------------------------------------------------------------------------- #
        # # DEPTH
        # # ---------------------------------------------------------------------------- #
        if self.data_type.get('depth', False):
            depth = self.cameras.get_depth()
            for camera_name in depth.keys():
                pkl_dic['observation'][camera_name].update(depth[camera_name])
        # # ---------------------------------------------------------------------------- #
        # # endpose JSON
        # # ---------------------------------------------------------------------------- #
        if self.data_type.get('endpose', False):
            
            def trans_endpose_quat2rpy(endpose, gripper_val):
                rpy = t3d.euler.quat2euler(endpose[-4:])
                roll, pitch, yaw = rpy
                x,y,z = endpose[:3]
                endpose = {
                    "gripper": float(gripper_val),
                    "pitch" : float(pitch),
                    "roll" : float(roll),
                    "x": float(x),
                    "y": float(y),
                    "yaw" : float(yaw),
                    "z": float(z),
                }
                return endpose

            # TODO
            norm_gripper_val = [self.robot.get_left_gripper_val(), self.robot.get_right_gripper_val()]
            left_endpose = trans_endpose_quat2rpy(self.robot.get_left_endpose(), norm_gripper_val[0])
            right_endpose = trans_endpose_quat2rpy(self.robot.get_right_endpose(), norm_gripper_val[1])

            # tmp
            # left_endpose = trans_endpose_quat2rpy(self.robot.get_left_orig_endpose(), norm_gripper_val[0])
            # right_endpose = trans_endpose_quat2rpy(self.robot.get_right_orig_endpose(), norm_gripper_val[1])

            if self.dual_arm:
                pkl_dic["endpose"] = np.array([left_endpose["x"],left_endpose["y"],left_endpose["z"],left_endpose["roll"],
                                            left_endpose["pitch"],left_endpose["yaw"],left_endpose["gripper"],
                                            right_endpose["x"],right_endpose["y"],right_endpose["z"],right_endpose["roll"],
                                            right_endpose["pitch"],right_endpose["yaw"],right_endpose["gripper"],])
            else:
                pkl_dic["endpose"] = np.array([right_endpose["x"],right_endpose["y"],right_endpose["z"],right_endpose["roll"],
                                                right_endpose["pitch"],right_endpose["yaw"],right_endpose["gripper"],])

            # tmp
            # pkl_dic["endpose"] = self.robot.get_left_orig_endpose()
        # # ---------------------------------------------------------------------------- #
        # # JointState JSON
        # # ---------------------------------------------------------------------------- #
        if self.data_type.get('qpos', False):
            
            left_jointstate = self.robot.get_left_arm_jointState()
            right_jointstate = self.robot.get_right_arm_jointState()

            #tmp
            # left_jointstate = self.robot.get_left_arm_real_jointState()
            # right_jointstate = self.robot.get_right_arm_real_jointState()
            
            if self.dual_arm:
                pkl_dic["joint_action"] = np.array(left_jointstate+right_jointstate)
            else:
                pkl_dic["joint_action"] = np.array(right_jointstate)
            
        # # ---------------------------------------------------------------------------- #
        # # PointCloud
        # # ---------------------------------------------------------------------------- #      
        if self.data_type.get('pointcloud', False):
            pkl_dic["pointcloud"] = self.cameras.get_pcd(self.data_type.get("conbine", False))
        #===========================================================#
        self.now_obs = pkl_dic
        return pkl_dic
        
    def get_cam_obs(self, observation: dict) -> dict:
        head_cam = np.moveaxis(observation['observation']['head_camera']['rgb'], -1, 0) / 255
        # front_cam = np.moveaxis(observation['observation']['front_camera']['rgb'], -1, 0) / 255
        left_cam = np.moveaxis(observation['observation']['left_camera']['rgb'], -1, 0) / 255
        right_cam = np.moveaxis(observation['observation']['right_camera']['rgb'], -1, 0) / 255
        return dict(
            head_cam = head_cam,
            # front_cam = front_cam,
            left_cam = left_cam,
            right_cam = right_cam
        )

    def take_action(self, action):
        if self.take_action_cnt == self.step_lim:
            return

        eval_video_freq = 15
        
        if self.eval_video_path is not None and self.take_action_cnt % eval_video_freq == 0:
            self.eval_video_ffmpeg.stdin.write(self.now_obs['observation']['head_camera']['rgb'].tobytes())

        self.take_action_cnt += 1
        print(f'step: {self.take_action_cnt} / {self.step_lim}', end='\r')

        self._update_render()
        if self.render_freq:
            self.viewer.render()
        
        actions = np.array([action])
        left_jointstate = self.robot.get_left_arm_jointState()
        right_jointstate = self.robot.get_right_arm_jointState()
        current_jointstate = np.array(left_jointstate + right_jointstate)

        left_arm_actions , left_gripper_actions , left_current_qpos, left_path = [], [], [], []
        right_arm_actions , right_gripper_actions , right_current_qpos, right_path = [], [], [], []
        
        if self.dual_arm:
            left_arm_actions,left_gripper_actions = actions[:, :6],actions[:, 6]
            right_arm_actions,right_gripper_actions = actions[:, 7:13],actions[:, 13]
            left_current_qpos, right_current_qpos = current_jointstate[:6], current_jointstate[7:13]
            left_current_gripper, right_current_gripper = current_jointstate[6:7], current_jointstate[13:14] 
        else:
            right_arm_actions,right_gripper_actions = actions[:, :6],actions[:, 6]
            right_current_qpos = current_jointstate[:6]
            right_current_gripper = current_jointstate[6:7]
        
        if self.dual_arm:
            left_path = np.vstack((left_current_qpos, left_arm_actions))
            left_gripper_path = np.hstack((left_current_gripper, left_gripper_actions))
            
        right_path = np.vstack((right_current_qpos, right_arm_actions))
        right_gripper_path = np.hstack((right_current_gripper, right_gripper_actions))

        topp_left_flag, topp_right_flag = True, True
        try:
            times, left_pos, left_vel, acc, duration = self.robot.left_planner.TOPP(left_path, 1/250, verbose=True)
            left_result = dict()
            left_result['position'], left_result['velocity'] = left_pos, left_vel
            left_n_step = left_result["position"].shape[0]
            left_gripper = np.linspace(left_gripper_path[0], left_gripper_path[-1], left_n_step)
        except Exception as e:
            print('left arm TOPP error: ', e)
            topp_left_flag = False
            left_n_step = 1
        
        if left_n_step == 0 or (not self.dual_arm):
            topp_left_flag = False
            left_n_step = 1

        try:
            times, right_pos, right_vel, acc, duration = self.robot.right_planner.TOPP(right_path, 1/250, verbose=True)            
            right_result = dict()
            right_result['position'], right_result['velocity'] = right_pos, right_vel
            right_n_step = right_result["position"].shape[0]
            right_gripper = np.linspace(right_gripper_path[0], right_gripper_path[-1], right_n_step)
        except Exception as e:
            print('right arm TOPP error: ', e)
            topp_right_flag = False
            right_n_step = 1
    
        if right_n_step == 0:
            topp_right_flag = False
            right_n_step = 1
    
        n_step = max(left_n_step, right_n_step)

        now_left_id = 0 if topp_left_flag else 1e9
        now_right_id = 0 if topp_right_flag else 1e9

        while now_left_id < left_n_step or now_right_id < right_n_step:
            if topp_left_flag and now_left_id < left_n_step and now_left_id / left_n_step <= now_right_id / right_n_step:
                self.robot.set_arm_joints(left_result['position'][now_left_id], left_result['velocity'][now_left_id],'left')
                if not self.fix_gripper: 
                    self.robot.set_gripper(left_gripper[now_left_id], 'left')

                now_left_id +=1
                
            if topp_right_flag and now_right_id < right_n_step and now_right_id / right_n_step <= now_left_id / left_n_step:
                self.robot.set_arm_joints(right_result['position'][now_right_id], right_result['velocity'][now_right_id],'right')
                if not self.fix_gripper:
                    self.robot.set_gripper(right_gripper[now_right_id], 'right')

                now_right_id +=1
            
            self._step()
            self._update_render()

            self.cvpr_score = max(self.cvpr_score, self.stage_reward())
            if self.check_success():
                self.eval_success_cvpr = True
                return
    
        self. _update_render()

        if self.render_freq:
            self.viewer.render()
